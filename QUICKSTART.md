# GameGUI Quick Start Guide

## ✅ Build Status

**The project has been successfully built!**

Output: `bin/Release/netstandard2.1/GameGUI.Core.dll`

## 📦 What You Have

### Pure C# Core Library
- **Location**: `bin/Release/netstandard2.1/GameGUI.Core.dll`
- **Framework**: .NET Standard 2.1
- **Dependencies**: None (pure C#, no Unity dependencies)
- **Size**: Lightweight, production-ready

### Unity Integration Package
- **Location**: `Unity/GameGUI.Unity/`
- **Contents**: Unity bridge scripts, renderer, helpers, and examples
- **Ready to use**: Just copy to Unity's Packages folder

## 🚀 How to Use in Unity

### Method 1: Unity Package (Recommended)

1. **Copy the Unity package to your Unity project:**
   ```
   Copy: Unity/GameGUI.Unity/
   To: YourUnityProject/Packages/GameGUI.Unity/
   ```

2. **Copy the core DLL:**
   ```
   Copy: bin/Release/netstandard2.1/GameGUI.Core.dll
   To: YourUnityProject/Packages/GameGUI.Unity/Runtime/Plugins/
   ```

3. **Unity will automatically import the package**

### Method 2: Manual Installation

1. **Copy the DLL to your Unity project:**
   ```
   Copy: bin/Release/netstandard2.1/GameGUI.Core.dll
   To: YourUnityProject/Assets/Plugins/
   ```

2. **Copy Unity integration scripts:**
   ```
   Copy: Unity/GameGUI.Unity/Runtime/*.cs
   To: YourUnityProject/Assets/Scripts/GameGUI/
   ```

## 💻 Basic Usage Example

### 1. Create a GameObject with UnityGUIBridge

```csharp
using GameGUI.Unity;
using GameGUI.Elements;
using UnityEngine;

public class MyGameUI : MonoBehaviour
{
    private UnityGUIBridge guiBridge;
    
    void Start()
    {
        // Get or add the bridge component
        guiBridge = gameObject.AddComponent<UnityGUIBridge>();
        
        // Create your UI
        CreateMainMenu();
    }
    
    void CreateMainMenu()
    {
        // Create a simple menu using the helper
        var menu = UnityGUIHelper.CreateSimpleMenu("Main Menu",
            ("Start Game", StartGame),
            ("Options", ShowOptions),
            ("Quit", QuitGame)
        );
        
        guiBridge.AddElement(menu);
    }
    
    void StartGame()
    {
        Debug.Log("Starting game...");
        // Your game start logic
    }
    
    void ShowOptions()
    {
        Debug.Log("Showing options...");
        // Your options logic
    }
    
    void QuitGame()
    {
        Application.Quit();
    }
}
```

### 2. Create Custom UI Elements

```csharp
using GameGUI.Unity;
using GameGUI.Elements;
using GameGUI.Core;
using UnityEngine;

public class CustomUI : MonoBehaviour
{
    private UnityGUIBridge guiBridge;
    
    void Start()
    {
        guiBridge = GetComponent<UnityGUIBridge>();
        
        // Create a button
        var button = UnityGUIHelper.CreateButton(
            "Click Me!",
            new Vector2(100, 100),
            new Vector2(150, 40),
            OnButtonClick
        );
        
        guiBridge.AddElement(button);
        
        // Create a text input
        var textBox = UnityGUIHelper.CreateTextBox(
            "Enter your name...",
            new Vector2(100, 150),
            new Vector2(200, 30)
        );
        
        textBox.TextChanged += OnTextChanged;
        guiBridge.AddElement(textBox);
        
        // Create a label
        var label = UnityGUIHelper.CreateLabel(
            "Welcome to GameGUI!",
            new Vector2(100, 200),
            new Vector2(300, 30)
        );
        
        guiBridge.AddElement(label);
    }
    
    void OnButtonClick()
    {
        Debug.Log("Button was clicked!");
    }
    
    void OnTextChanged(TextBox textBox, string newText)
    {
        Debug.Log($"Text changed to: {newText}");
    }
}
```

## 🎨 Using Themes

```csharp
using GameGUI.Theming;
using GameGUI.Unity;

// Create a dark theme
var darkTheme = GUITheme.CreateDarkTheme();

// Apply to an element
darkTheme.ApplyToElement(myButton);

// Or create a custom theme
var customTheme = new GUITheme("Custom")
{
    Colors = new ThemeColors
    {
        Primary = GameGUI.Core.Color.FromHex("#FF6B6B"),
        Secondary = GameGUI.Core.Color.FromHex("#4ECDC4"),
        Background = GameGUI.Core.Color.FromHex("#2C3E50")
    }
};
```

## 📚 Examples Included

### Basic Example
- Location: `Unity/GameGUI.Unity/Samples~/BasicExamples/`
- Shows: Button creation, text input, theming, FPS display

### Advanced Example
- Location: `Unity/GameGUI.Unity/Samples~/AdvancedExamples/`
- Shows: Custom elements (ProgressBar, Slider), layouts, dynamic content

## 🔧 Rebuilding the Core Library

If you make changes to the core library:

```bash
# Navigate to project root
cd "c:\Users\<USER>\Documents\C#"

# Build release version
dotnet build --configuration Release

# Output will be in: bin/Release/netstandard2.1/GameGUI.Core.dll
```

## 📖 Documentation

- **README.md**: Full documentation and architecture overview
- **BUILD.md**: Detailed build instructions and troubleshooting
- **Unity/GameGUI.Unity/README.md**: Unity-specific documentation

## ⚡ Key Features

✅ **Pure C# Core** - No Unity dependencies in the core library  
✅ **Unity Integration** - Seamless Unity bridge with MonoBehaviour components  
✅ **Rich Elements** - Buttons, Labels, TextBoxes, Images, Panels, and more  
✅ **Layout System** - Stack and Grid layouts for automatic positioning  
✅ **Theming** - Built-in dark/light themes plus custom theme support  
✅ **Event System** - Comprehensive input handling and event management  
✅ **Extensible** - Easy to create custom elements and layouts  

## 🎯 Next Steps

1. **Try the examples** - Import the samples into Unity and run them
2. **Create your first UI** - Use the quick start code above
3. **Customize** - Create custom elements and themes
4. **Extend** - Build on top of the system for your specific needs

## 💡 Tips

- Use `UnityGUIHelper` for quick element creation
- Apply themes for consistent styling
- Check the examples for advanced usage patterns
- The core library can be used in any C# project, not just Unity

## 🐛 Troubleshooting

**GUI not appearing?**
- Ensure UnityGUIBridge component is added and enabled
- Check that elements are added to the root or a parent

**Build errors?**
- Make sure you're using .NET SDK 6.0 or later
- Check that Unity files are excluded from core build

**Input not working?**
- Verify Interactive property is true on elements
- Check camera reference in UnityGUIBridge

---

**You're all set! The library is built and ready to use in Unity! 🎉**

