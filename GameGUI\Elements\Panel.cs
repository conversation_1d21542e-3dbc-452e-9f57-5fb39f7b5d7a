using GameGUI.Core;

namespace GameGUI.Elements
{
    /// <summary>
    /// Basic container panel element
    /// </summary>
    public class Panel : GUIElement
    {
        public Panel()
        {
            Style.BackgroundColor = Color.Gray;
            Style.BorderColor = Color.Black;
            Style.BorderWidth = 1;
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            var style = Style;
            
            // Draw background
            if (style.BackgroundColor.A > 0)
            {
                if (style.BorderRadius > 0)
                {
                    renderer.DrawRoundedRect(bounds, style.BorderRadius, style.BackgroundColor);
                }
                else
                {
                    renderer.DrawRect(bounds, style.BackgroundColor);
                }
            }
            
            // Draw background image if specified
            if (!string.IsNullOrEmpty(style.BackgroundImage))
            {
                renderer.DrawImage(style.BackgroundImage, bounds, null, Color.White);
            }
            
            // Draw border
            if (style.BorderWidth > 0 && style.BorderColor.A > 0)
            {
                if (style.BorderRadius > 0)
                {
                    renderer.DrawRoundedRectOutline(bounds, style.BorderRadius, style.BorderColor, style.BorderWidth);
                }
                else
                {
                    renderer.DrawRectOutline(bounds, style.BorderColor, style.BorderWidth);
                }
            }
            
            // Draw shadow
            if (style.ShadowColor.A > 0 && style.ShadowBlur > 0)
            {
                var shadowBounds = new Rect(
                    bounds.X + style.ShadowOffset.X,
                    bounds.Y + style.ShadowOffset.Y,
                    bounds.Width,
                    bounds.Height
                );
                renderer.DrawShadow(shadowBounds, style.ShadowOffset, style.ShadowBlur, style.ShadowColor);
            }
        }
    }
}
