using System.Text;
using System.Collections.Generic;

namespace GameGUI.Unity.Editor
{
    /// <summary>
    /// Generates C# scripts from GUI designs
    /// </summary>
    public class GUIScriptGenerator
    {
        public string GenerateScript(GUIDesignAsset design)
        {
            var sb = new StringBuilder();
            
            // Header
            sb.AppendLine("using UnityEngine;");
            sb.AppendLine("using GameGUI.Unity;");
            sb.AppendLine("using GameGUI.Elements;");
            sb.AppendLine("using GameGUI.Core;");
            sb.AppendLine();
            sb.AppendLine("/// <summary>");
            sb.AppendLine($"/// Auto-generated GUI script for: {design.designName}");
            sb.AppendLine("/// Generated by GameGUI Designer");
            sb.AppendLine("/// </summary>");
            sb.AppendLine($"public class {SanitizeClassName(design.designName)} : MonoBehaviour");
            sb.AppendLine("{");
            sb.AppendLine("    private UnityGUIBridge guiBridge;");
            sb.AppendLine();
            
            // Element references
            sb.AppendLine("    // Element references");
            GenerateElementReferences(sb, design.rootElements);
            sb.AppendLine();
            
            // Start method
            sb.AppendLine("    void Start()");
            sb.AppendLine("    {");
            sb.AppendLine("        guiBridge = gameObject.AddComponent<UnityGUIBridge>();");
            sb.AppendLine($"        guiBridge.SetReferenceResolution(new Vector2({design.referenceResolution.x}f, {design.referenceResolution.y}f));");
            sb.AppendLine("        CreateGUI();");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // CreateGUI method
            sb.AppendLine("    void CreateGUI()");
            sb.AppendLine("    {");
            
            foreach (var element in design.rootElements)
            {
                GenerateElementCreation(sb, element, "        ", null);
            }
            
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // Event handlers
            sb.AppendLine("    // Event Handlers");
            GenerateEventHandlers(sb, design.rootElements);
            
            sb.AppendLine("}");
            
            return sb.ToString();
        }
        
        private void GenerateElementReferences(StringBuilder sb, List<GUIElementData> elements)
        {
            foreach (var element in elements)
            {
                var varName = SanitizeVariableName(element.name);
                sb.AppendLine($"    private {GetElementClassName(element.elementType)} {varName};");
                
                if (element.children.Count > 0)
                {
                    GenerateElementReferences(sb, element.children);
                }
            }
        }
        
        private void GenerateElementCreation(StringBuilder sb, GUIElementData element, string indent, string parentVar)
        {
            var varName = SanitizeVariableName(element.name);
            var className = GetElementClassName(element.elementType);
            
            // Create element
            sb.AppendLine($"{indent}// Create {element.name}");
            sb.AppendLine($"{indent}{varName} = new {className}");
            sb.AppendLine($"{indent}{{");
            sb.AppendLine($"{indent}    Name = \"{element.name}\",");
            sb.AppendLine($"{indent}    Position = new GameGUI.Core.Vector2({element.position.x}f, {element.position.y}f),");
            sb.AppendLine($"{indent}    Size = new GameGUI.Core.Vector2({element.size.x}f, {element.size.y}f),");
            sb.AppendLine($"{indent}    Visible = {element.visible.ToString().ToLower()},");
            sb.AppendLine($"{indent}    Enabled = {element.enabled.ToString().ToLower()},");
            sb.AppendLine($"{indent}    Interactive = {element.interactive.ToString().ToLower()},");
            
            // Element-specific properties
            if (!string.IsNullOrEmpty(element.text) && (element.elementType == "Button" || element.elementType == "Label"))
            {
                sb.AppendLine($"{indent}    Text = \"{element.text}\",");
            }
            
            if (element.elementType == "TextBox" && !string.IsNullOrEmpty(element.placeholder))
            {
                sb.AppendLine($"{indent}    Placeholder = \"{element.placeholder}\",");
                sb.AppendLine($"{indent}    IsPassword = {element.isPassword.ToString().ToLower()},");
            }
            
            if (element.elementType == "Image" && !string.IsNullOrEmpty(element.imagePath))
            {
                sb.AppendLine($"{indent}    ImagePath = \"{element.imagePath}\",");
            }
            
            // Style
            sb.AppendLine($"{indent}    Style = new GUIStyle");
            sb.AppendLine($"{indent}    {{");
            sb.AppendLine($"{indent}        BackgroundColor = new GameGUI.Core.Color({element.backgroundColor.r}f, {element.backgroundColor.g}f, {element.backgroundColor.b}f, {element.backgroundColor.a}f),");
            sb.AppendLine($"{indent}        TextColor = new GameGUI.Core.Color({element.textColor.r}f, {element.textColor.g}f, {element.textColor.b}f, {element.textColor.a}f),");
            sb.AppendLine($"{indent}        BorderColor = new GameGUI.Core.Color({element.borderColor.r}f, {element.borderColor.g}f, {element.borderColor.b}f, {element.borderColor.a}f),");
            sb.AppendLine($"{indent}        BorderWidth = {element.borderWidth}f,");
            sb.AppendLine($"{indent}        FontSize = {element.fontSize}f,");
            sb.AppendLine($"{indent}    }}");
            sb.AppendLine($"{indent}}};");
            sb.AppendLine();
            
            // Add event handlers for buttons
            if (element.elementType == "Button")
            {
                sb.AppendLine($"{indent}{varName}.OnButtonClick += On{SanitizeMethodName(element.name)}Click;");
            }
            
            if (element.elementType == "TextBox")
            {
                sb.AppendLine($"{indent}{varName}.TextChanged += On{SanitizeMethodName(element.name)}TextChanged;");
            }
            
            // Add to parent or root
            if (parentVar != null)
            {
                sb.AppendLine($"{indent}guiBridge.AddElement({varName}, {parentVar});");
            }
            else
            {
                sb.AppendLine($"{indent}guiBridge.AddElement({varName});");
            }
            
            sb.AppendLine();
            
            // Create children
            foreach (var child in element.children)
            {
                GenerateElementCreation(sb, child, indent, varName);
            }
        }
        
        private void GenerateEventHandlers(StringBuilder sb, List<GUIElementData> elements)
        {
            foreach (var element in elements)
            {
                if (element.elementType == "Button")
                {
                    var methodName = $"On{SanitizeMethodName(element.name)}Click";
                    sb.AppendLine($"    void {methodName}(Button button)");
                    sb.AppendLine("    {");
                    sb.AppendLine($"        Debug.Log(\"{element.name} clicked!\");");
                    sb.AppendLine("        // TODO: Implement button click logic");
                    sb.AppendLine("    }");
                    sb.AppendLine();
                }
                
                if (element.elementType == "TextBox")
                {
                    var methodName = $"On{SanitizeMethodName(element.name)}TextChanged";
                    sb.AppendLine($"    void {methodName}(TextBox textBox, string newText)");
                    sb.AppendLine("    {");
                    sb.AppendLine($"        Debug.Log(\"{element.name} text changed: \" + newText);");
                    sb.AppendLine("        // TODO: Implement text changed logic");
                    sb.AppendLine("    }");
                    sb.AppendLine();
                }
                
                if (element.children.Count > 0)
                {
                    GenerateEventHandlers(sb, element.children);
                }
            }
        }
        
        private string GetElementClassName(string elementType)
        {
            switch (elementType)
            {
                case "Panel": return "Panel";
                case "Button": return "Button";
                case "Label": return "Label";
                case "TextBox": return "TextBox";
                case "Image": return "Image";
                default: return "Panel";
            }
        }
        
        private string SanitizeClassName(string name)
        {
            // Remove spaces and special characters
            name = name.Replace(" ", "");
            name = System.Text.RegularExpressions.Regex.Replace(name, @"[^a-zA-Z0-9_]", "");
            
            // Ensure it starts with a letter
            if (name.Length > 0 && char.IsDigit(name[0]))
            {
                name = "GUI_" + name;
            }
            
            return name;
        }
        
        private string SanitizeVariableName(string name)
        {
            // Convert to camelCase
            name = SanitizeClassName(name);
            if (name.Length > 0)
            {
                name = char.ToLower(name[0]) + name.Substring(1);
            }
            return name;
        }
        
        private string SanitizeMethodName(string name)
        {
            // Convert to PascalCase
            name = SanitizeClassName(name);
            if (name.Length > 0)
            {
                name = char.ToUpper(name[0]) + name.Substring(1);
            }
            return name;
        }
    }
}
