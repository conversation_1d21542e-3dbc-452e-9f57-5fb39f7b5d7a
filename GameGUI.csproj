<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <LangVersion>8.0</LangVersion>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>GameGUI.Core</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>GameGUI Contributors</Authors>
    <Description>A pure C# GUI system designed for Unity integration. Core library with no Unity dependencies.</Description>
    <PackageTags>gui;ui;game;unity;gamedev;csharp</PackageTags>
    <RepositoryUrl>https://github.com/yourusername/GameGUI</RepositoryUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <AssemblyName>GameGUI.Core</AssemblyName>
    <RootNamespace>GameGUI</RootNamespace>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>

  <!-- Exclude Unity-specific files from the core library build -->
  <ItemGroup>
    <Compile Remove="Unity\**" />
    <EmbeddedResource Remove="Unity\**" />
    <None Remove="Unity\**" />
  </ItemGroup>

  <!-- Exclude GameGUI/Unity folder (old location) -->
  <ItemGroup>
    <Compile Remove="GameGUI\Unity\**" />
    <EmbeddedResource Remove="GameGUI\Unity\**" />
    <None Remove="GameGUI\Unity\**" />
  </ItemGroup>

</Project>
