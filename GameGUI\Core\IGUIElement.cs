using System;
using System.Collections.Generic;

namespace GameGUI.Core
{
    /// <summary>
    /// Base interface for all GUI elements
    /// </summary>
    public interface IGUIElement
    {
        string Id { get; set; }
        string Name { get; set; }
        IGUIElement Parent { get; set; }
        IList<IGUIElement> Children { get; }
        
        // Transform properties
        Vector2 Position { get; set; }
        Vector2 Size { get; set; }
        Vector2 Anchor { get; set; }
        Vector2 Pivot { get; set; }
        float Rotation { get; set; }
        Vector2 Scale { get; set; }
        
        // Visibility and interaction
        bool Visible { get; set; }
        bool Enabled { get; set; }
        bool Interactive { get; set; }
        int ZOrder { get; set; }
        
        // Style
        GUIStyle Style { get; set; }
        
        // Events
        event Action<IGUIElement> OnClick;
        event Action<IGUIElement> OnHover;
        event Action<IGUIElement> OnFocus;
        event Action<IGUIElement> OnBlur;
        
        // Methods
        void AddChild(IGUIElement child);
        void RemoveChild(IGUIElement child);
        void Update(float deltaTime);
        void Render(IGUIRenderer renderer);
        bool ContainsPoint(Vector2 point);
        void SetParent(IGUIElement parent);
        
        // Layout
        void CalculateLayout();
        Rect GetBounds();
        Vector2 GetWorldPosition();

        // Event triggers (for internal use by event system)
        void TriggerClick();
        void TriggerHover();
        void TriggerFocus();
        void TriggerBlur();
    }
}
