using UnityEngine;
using GameGUI.Unity;
using GameGUI.Core;
using GameGUI.Elements;

namespace GameGUI.Examples
{
    /// <summary>
    /// Super easy Unity example showing how simple it is to create UIs
    /// Just attach this to a GameObject and run!
    /// </summary>
    public class SuperEasyGUIExample : MonoBehaviour
    {
        private UnityGUIBridge guiBridge;
        private Panel mainMenu;
        private Panel settingsPanel;
        private Panel gameHUD;
        private bool showSettings = false;
        
        void Start()
        {
            // Get or create the GUI bridge
            guiBridge = GetComponent<UnityGUIBridge>();
            if (guiBridge == null)
            {
                guiBridge = gameObject.AddComponent<UnityGUIBridge>();
            }
            
            // Wait for initialization
            StartCoroutine(InitializeGUI());
        }
        
        System.Collections.IEnumerator InitializeGUI()
        {
            // Wait for bridge to initialize
            while (!guiBridge.IsInitialized)
            {
                yield return null;
            }
            
            CreateMainMenu();
        }
        
        void CreateMainMenu()
        {
            // Create a beautiful main menu in just a few lines!
            mainMenu = UI.VerticalLayout()
                .At(1920/2 - 150, 1080/2 - 200)
                .Padding(30)
                .Spacing(20)
                .Children(
                    UI.Label("GAME TITLE")
                        .FontSize(32)
                        .TextColor(Color.FromHex("#FFFFFF"))
                        .Align(TextAlignment.Center),
                    
                    UI.Button("Start Game")
                        .Width(300)
                        .Height(50)
                        .BackgroundColor(Color.FromHex("#4CAF50"))
                        .TextColor(Color.White)
                        .BorderRadius(10)
                        .OnClick(btn => StartGame()),
                    
                    UI.Button("Settings")
                        .Width(300)
                        .Height(50)
                        .BackgroundColor(Color.FromHex("#2196F3"))
                        .TextColor(Color.White)
                        .BorderRadius(10)
                        .OnClick(btn => ShowSettings()),
                    
                    UI.Button("Quit")
                        .Width(300)
                        .Height(50)
                        .BackgroundColor(Color.FromHex("#F44336"))
                        .TextColor(Color.White)
                        .BorderRadius(10)
                        .OnClick(btn => QuitGame())
                )
                .Build();
            
            mainMenu.Style.BackgroundColor = Color.FromHex("#1E1E1E");
            mainMenu.Style.BorderRadius = 15;
            
            guiBridge.RootElement.AddChild(mainMenu);
        }
        
        void ShowSettings()
        {
            if (settingsPanel != null)
            {
                settingsPanel.Visible = true;
                mainMenu.Visible = false;
                return;
            }
            
            // Create settings panel
            settingsPanel = UI.VerticalLayout()
                .At(1920/2 - 250, 1080/2 - 300)
                .Padding(30)
                .Spacing(20)
                .Children(
                    UI.Label("Settings")
                        .FontSize(28)
                        .TextColor(Color.White)
                        .Align(TextAlignment.Center),
                    
                    // Volume slider
                    UI.VerticalLayout()
                        .Spacing(10)
                        .Children(
                            UI.Label("Master Volume")
                                .TextColor(Color.White),
                            UI.Slider(0, 100, 75)
                                .Width(400)
                                .OnValueChanged((slider, value) => 
                                    AudioListener.volume = value / 100f)
                        ),
                    
                    // Graphics quality
                    UI.VerticalLayout()
                        .Spacing(10)
                        .Children(
                            UI.Label("Graphics Quality")
                                .TextColor(Color.White),
                            UI.Dropdown("Low", "Medium", "High", "Ultra")
                                .Width(400)
                                .SelectedIndex(QualitySettings.GetQualityLevel())
                                .OnSelectionChanged((dd, index) => 
                                    QualitySettings.SetQualityLevel(index))
                        ),
                    
                    // Fullscreen toggle
                    UI.Toggle("Fullscreen", Screen.fullScreen)
                        .AsSwitch()
                        .OnValueChanged((toggle, isOn) => 
                            Screen.fullScreen = isOn),
                    
                    // VSync toggle
                    UI.Toggle("VSync", QualitySettings.vSyncCount > 0)
                        .OnValueChanged((toggle, isOn) => 
                            QualitySettings.vSyncCount = isOn ? 1 : 0),
                    
                    // Back button
                    UI.Button("Back")
                        .Width(200)
                        .Height(45)
                        .BackgroundColor(Color.FromHex("#757575"))
                        .TextColor(Color.White)
                        .BorderRadius(8)
                        .OnClick(btn => HideSettings())
                )
                .Build();
            
            settingsPanel.Style.BackgroundColor = Color.FromHex("#1E1E1E");
            settingsPanel.Style.BorderRadius = 15;
            
            guiBridge.RootElement.AddChild(settingsPanel);
            mainMenu.Visible = false;
        }
        
        void HideSettings()
        {
            if (settingsPanel != null)
                settingsPanel.Visible = false;
            mainMenu.Visible = true;
        }
        
        void StartGame()
        {
            mainMenu.Visible = false;
            if (settingsPanel != null)
                settingsPanel.Visible = false;
            
            CreateGameHUD();
        }
        
        void CreateGameHUD()
        {
            // Create a complete game HUD
            var healthBar = UI.ProgressBar(0, 100, 100)
                .Width(250)
                .Height(30)
                .CustomText("Health")
                .Build();
            healthBar.SetColor(Color.FromHex("#F44336"));
            
            var manaBar = UI.ProgressBar(0, 100, 100)
                .Width(250)
                .Height(30)
                .CustomText("Mana")
                .Build();
            manaBar.SetColor(Color.FromHex("#2196F3"));
            
            var scoreLabel = UI.Label("Score: 0")
                .FontSize(24)
                .TextColor(Color.White)
                .Build();
            
            var fpsLabel = UI.Label("FPS: 60")
                .FontSize(16)
                .TextColor(Color.FromHex("#00FF00"))
                .Build();
            
            gameHUD = UI.Panel()
                .Size(1920, 1080)
                .BackgroundColor(new Color(0, 0, 0, 0))
                .Children(
                    // Top-left stats
                    UI.VerticalLayout()
                        .At(20, 20)
                        .Spacing(10)
                        .Children(healthBar, manaBar, scoreLabel, fpsLabel)
                        .Build(),
                    
                    // Top-right mini-map
                    UI.Panel()
                        .At(1920 - 220, 20)
                        .Size(200, 200)
                        .BackgroundColor(Color.FromHex("#000000"))
                        .Border(Color.White, 2)
                        .BorderRadius(10)
                        .Children(
                            UI.Label("Mini Map")
                                .At(50, 90)
                                .TextColor(Color.White)
                        )
                        .Build(),
                    
                    // Bottom-center action bar
                    UI.HorizontalLayout()
                        .At(1920/2 - 250, 1080 - 120)
                        .Spacing(15)
                        .Children(
                            CreateActionButton("Q", Color.FromHex("#FF5722")),
                            CreateActionButton("W", Color.FromHex("#FF9800")),
                            CreateActionButton("E", Color.FromHex("#FFC107")),
                            CreateActionButton("R", Color.FromHex("#4CAF50")),
                            CreateActionButton("T", Color.FromHex("#2196F3"))
                        )
                        .Build(),
                    
                    // Pause button
                    UI.Button("Pause")
                        .At(1920 - 120, 1080 - 60)
                        .Size(100, 40)
                        .BackgroundColor(Color.FromHex("#757575"))
                        .TextColor(Color.White)
                        .BorderRadius(8)
                        .OnClick(btn => PauseGame())
                        .Build()
                )
                .Build();
            
            guiBridge.RootElement.AddChild(gameHUD);
            
            // Start FPS counter
            StartCoroutine(UpdateFPS(fpsLabel));
            
            // Simulate damage over time
            StartCoroutine(SimulateDamage(healthBar, manaBar));
        }
        
        Button CreateActionButton(string key, Color color)
        {
            return UI.Button(key)
                .Size(80, 80)
                .BackgroundColor(color)
                .TextColor(Color.White)
                .FontSize(24)
                .BorderRadius(10)
                .Border(Color.White, 2)
                .OnClick(btn => Debug.Log($"Ability {key} used!"))
                .Build();
        }
        
        System.Collections.IEnumerator UpdateFPS(Label fpsLabel)
        {
            while (gameHUD != null && gameHUD.Visible)
            {
                fpsLabel.Text = $"FPS: {(int)(1f / Time.deltaTime)}";
                yield return new WaitForSeconds(0.5f);
            }
        }
        
        System.Collections.IEnumerator SimulateDamage(ProgressBar health, ProgressBar mana)
        {
            while (gameHUD != null && gameHUD.Visible)
            {
                yield return new WaitForSeconds(2f);
                
                // Random damage
                health.Value -= Random.Range(5, 15);
                mana.Value -= Random.Range(3, 10);
                
                // Regenerate
                if (health.Value < 100)
                    health.Value += Random.Range(1, 5);
                if (mana.Value < 100)
                    mana.Value += Random.Range(2, 8);
                
                // Clamp values
                health.Value = Mathf.Clamp(health.Value, 0, 100);
                mana.Value = Mathf.Clamp(mana.Value, 0, 100);
            }
        }
        
        void PauseGame()
        {
            var pauseMenu = UI.VerticalLayout()
                .At(1920/2 - 150, 1080/2 - 150)
                .Padding(30)
                .Spacing(15)
                .Children(
                    UI.Label("PAUSED")
                        .FontSize(32)
                        .TextColor(Color.White)
                        .Align(TextAlignment.Center),
                    
                    UI.Button("Resume")
                        .Width(250)
                        .Height(45)
                        .BackgroundColor(Color.FromHex("#4CAF50"))
                        .TextColor(Color.White)
                        .OnClick(btn => {
                            btn.Parent.Visible = false;
                        }),
                    
                    UI.Button("Main Menu")
                        .Width(250)
                        .Height(45)
                        .BackgroundColor(Color.FromHex("#2196F3"))
                        .TextColor(Color.White)
                        .OnClick(btn => {
                            gameHUD.Visible = false;
                            mainMenu.Visible = true;
                            btn.Parent.Visible = false;
                        })
                )
                .Build();
            
            pauseMenu.Style.BackgroundColor = Color.FromHex("#1E1E1E");
            pauseMenu.Style.BorderRadius = 15;
            pauseMenu.ZOrder = 1000;
            
            guiBridge.RootElement.AddChild(pauseMenu);
        }
        
        void QuitGame()
        {
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
    }
}

