# Building GameGUI

This document explains how to build and use the GameGUI system.

## Prerequisites

- .NET SDK 6.0 or later (for building the core library)
- Unity 2019.4 or later (for Unity integration)
- Visual Studio 2019+ or Visual Studio Code (recommended)

## Building the Core Library

The core GameGUI library is a pure C# .NET Standard 2.1 library with no Unity dependencies.

### Command Line Build

```bash
# Navigate to the project root
cd GameGUI

# Restore dependencies
dotnet restore

# Build the library
dotnet build --configuration Release

# The output will be in bin/Release/netstandard2.1/
```

### Visual Studio Build

1. Open `GameGUI.csproj` in Visual Studio
2. Select "Release" configuration
3. Build → Build Solution
4. The DLL will be output to `bin/Release/netstandard2.1/GameGUI.Core.dll`

## Unity Integration

### Option 1: Unity Package (Recommended)

1. Copy the entire `Unity/GameGUI.Unity` folder to your Unity project's `Packages` folder
2. Unity will automatically detect and import the package
3. The core library DLL should be included in the package

### Option 2: Manual Integration

1. Build the core library as described above
2. Copy `GameGUI.Core.dll` to your Unity project's `Assets/Plugins` folder
3. Copy the Unity integration scripts from `Unity/GameGUI.Unity/Runtime` to your project
4. Copy the examples from `Unity/GameGUI.Unity/Samples~` if desired

## Development Setup

### For Core Library Development

1. Clone the repository
2. Open `GameGUI.csproj` in your preferred IDE
3. Make changes to the core library
4. Build and test

### For Unity Integration Development

1. Set up the core library as above
2. Open Unity and create a new project
3. Import the GameGUI Unity package
4. Create test scenes using the provided examples

## Testing

### Core Library Tests

```bash
# Run unit tests (if implemented)
dotnet test
```

### Unity Testing

1. Import the package into a Unity project
2. Open the sample scenes in `Samples~/BasicExamples` and `Samples~/AdvancedExamples`
3. Play the scenes to test functionality

## Distribution

### Core Library NuGet Package

```bash
# Pack the library
dotnet pack --configuration Release

# The .nupkg file will be in bin/Release/
```

### Unity Package

The Unity package is already structured correctly in `Unity/GameGUI.Unity/`. To distribute:

1. Zip the entire `GameGUI.Unity` folder
2. Users can extract it to their `Packages` folder
3. Or submit to Unity Asset Store following their guidelines

## Troubleshooting

### Common Build Issues

**Issue**: "Could not find GameGUI.Core.dll"
**Solution**: Ensure the core library is built and the DLL is in the correct location

**Issue**: "Assembly reference errors in Unity"
**Solution**: Check that the assembly definition files are correctly configured

**Issue**: "Namespace not found errors"
**Solution**: Ensure using statements are correct and assemblies are referenced

### Unity-Specific Issues

**Issue**: "GUI not rendering"
**Solution**: Ensure UnityGUIBridge component is added to a GameObject and enabled

**Issue**: "Input not working"
**Solution**: Check that the camera reference is set correctly in UnityGUIBridge

**Issue**: "Scaling issues on different resolutions"
**Solution**: Adjust the reference resolution in UnityGUIBridge settings

## Performance Considerations

- The system uses Unity's immediate mode GUI, which is suitable for game UIs but not high-frequency updates
- For best performance, minimize the number of GUI elements updated per frame
- Use object pooling for dynamic elements that are frequently created/destroyed
- Consider using Unity's new UI Toolkit for complex UIs with many elements

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Ensure all tests pass
5. Submit a pull request

Please follow the existing code style and add tests for new functionality.
