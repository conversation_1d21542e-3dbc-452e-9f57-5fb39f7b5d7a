using GameGUI.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GameGUI.Layout
{
    /// <summary>
    /// Layout manager that arranges elements in a grid
    /// </summary>
    public class GridLayout : LayoutManager
    {
        public int Columns { get; set; } = 1;
        public int Rows { get; set; } = 0; // 0 = auto-calculate
        public Vector2 Spacing { get; set; } = Vector2.Zero;
        public GridAlignment HorizontalAlignment { get; set; } = GridAlignment.Start;
        public GridAlignment VerticalAlignment { get; set; } = GridAlignment.Start;
        public bool FillCells { get; set; } = true;
        
        public GridLayout()
        {
        }
        
        public GridLayout(int columns, int rows = 0)
        {
            Columns = Math.Max(1, columns);
            Rows = Math.Max(0, rows);
        }
        
        public override void CalculateLayout(IGUIElement container, IList<IGUIElement> children)
        {
            if (children == null || children.Count == 0)
                return;
                
            var contentBounds = GetContentBounds(container);
            var visibleChildren = children.Where(c => c.Visible).ToList();
            
            if (visibleChildren.Count == 0)
                return;
                
            var gridInfo = CalculateGridInfo(visibleChildren.Count);
            var cellSize = CalculateCellSize(contentBounds, gridInfo);
            
            for (int i = 0; i < visibleChildren.Count; i++)
            {
                var child = visibleChildren[i];
                var gridPos = GetGridPosition(i, gridInfo.Columns);
                var cellBounds = GetCellBounds(contentBounds, gridPos, cellSize, gridInfo);
                
                PositionChildInCell(child, cellBounds);
            }
        }
        
        private GridInfo CalculateGridInfo(int childCount)
        {
            int columns = Math.Max(1, Columns);
            int rows = Rows > 0 ? Rows : (int)Math.Ceiling((double)childCount / columns);
            
            return new GridInfo { Columns = columns, Rows = rows };
        }
        
        private Vector2 CalculateCellSize(Rect contentBounds, GridInfo gridInfo)
        {
            float totalHorizontalSpacing = (gridInfo.Columns - 1) * Spacing.X;
            float totalVerticalSpacing = (gridInfo.Rows - 1) * Spacing.Y;
            
            float cellWidth = (contentBounds.Width - totalHorizontalSpacing) / gridInfo.Columns;
            float cellHeight = (contentBounds.Height - totalVerticalSpacing) / gridInfo.Rows;
            
            return new Vector2(Math.Max(0, cellWidth), Math.Max(0, cellHeight));
        }
        
        private Vector2 GetGridPosition(int index, int columns)
        {
            int row = index / columns;
            int col = index % columns;
            return new Vector2(col, row);
        }
        
        private Rect GetCellBounds(Rect contentBounds, Vector2 gridPos, Vector2 cellSize, GridInfo gridInfo)
        {
            float x = contentBounds.X + gridPos.X * (cellSize.X + Spacing.X);
            float y = contentBounds.Y + gridPos.Y * (cellSize.Y + Spacing.Y);
            
            return new Rect(x, y, cellSize.X, cellSize.Y);
        }
        
        private void PositionChildInCell(IGUIElement child, Rect cellBounds)
        {
            if (FillCells)
            {
                // Fill the entire cell
                child.Position = cellBounds.Position;
                child.Size = cellBounds.Size;
            }
            else
            {
                // Position based on alignment within the cell
                var childSize = child.Size;
                
                float x = cellBounds.X;
                float y = cellBounds.Y;
                
                // Horizontal alignment
                switch (HorizontalAlignment)
                {
                    case GridAlignment.Center:
                        x += (cellBounds.Width - childSize.X) * 0.5f;
                        break;
                    case GridAlignment.End:
                        x += cellBounds.Width - childSize.X;
                        break;
                }
                
                // Vertical alignment
                switch (VerticalAlignment)
                {
                    case GridAlignment.Center:
                        y += (cellBounds.Height - childSize.Y) * 0.5f;
                        break;
                    case GridAlignment.End:
                        y += cellBounds.Height - childSize.Y;
                        break;
                }
                
                child.Position = new Vector2(x, y);
            }
        }
        
        public override Vector2 GetPreferredSize(IGUIElement container, IList<IGUIElement> children)
        {
            if (children == null || children.Count == 0)
                return container.Size;
                
            var visibleChildren = children.Where(c => c.Visible).ToList();
            if (visibleChildren.Count == 0)
                return container.Size;
                
            var gridInfo = CalculateGridInfo(visibleChildren.Count);
            var style = container.Style;
            
            // Calculate preferred cell size based on largest child
            float maxChildWidth = visibleChildren.Max(c => c.Size.X);
            float maxChildHeight = visibleChildren.Max(c => c.Size.Y);
            
            float totalWidth = gridInfo.Columns * maxChildWidth + (gridInfo.Columns - 1) * Spacing.X;
            float totalHeight = gridInfo.Rows * maxChildHeight + (gridInfo.Rows - 1) * Spacing.Y;
            
            return new Vector2(
                totalWidth + style.Padding.Left + style.Padding.Right,
                totalHeight + style.Padding.Top + style.Padding.Bottom
            );
        }
        
        private struct GridInfo
        {
            public int Columns;
            public int Rows;
        }
    }
    
    public enum GridAlignment
    {
        Start,
        Center,
        End
    }
}
