using System;

namespace GameGUI.Core
{
    /// <summary>
    /// Style properties for GUI elements
    /// </summary>
    [Serializable]
    public class GUIStyle
    {
        // Background
        public Color BackgroundColor { get; set; } = Color.White;
        public string BackgroundImage { get; set; }
        public Vector2 BackgroundImageScale { get; set; } = Vector2.One;
        
        // Border
        public Color BorderColor { get; set; } = Color.Black;
        public float BorderWidth { get; set; } = 0;
        public float BorderRadius { get; set; } = 0;
        
        // Text
        public Color TextColor { get; set; } = Color.Black;
        public string FontFamily { get; set; } = "Arial";
        public float FontSize { get; set; } = 12;
        public FontStyle FontStyle { get; set; } = FontStyle.Normal;
        public TextAlignment TextAlignment { get; set; } = TextAlignment.Left;
        public TextVerticalAlignment TextVerticalAlignment { get; set; } = TextVerticalAlignment.Top;
        
        // Padding and Margin
        public Padding Padding { get; set; } = new Padding();
        public Padding Margin { get; set; } = new Padding();
        
        // Shadow
        public Color ShadowColor { get; set; } = Color.Transparent;
        public Vector2 ShadowOffset { get; set; } = Vector2.Zero;
        public float ShadowBlur { get; set; } = 0;
        
        // Animation
        public float TransitionDuration { get; set; } = 0.2f;
        public EasingType TransitionEasing { get; set; } = EasingType.EaseInOut;
        
        // State-specific styles
        public GUIStyle HoverStyle { get; set; }
        public GUIStyle PressedStyle { get; set; }
        public GUIStyle DisabledStyle { get; set; }
        public GUIStyle FocusedStyle { get; set; }
        
        public GUIStyle()
        {
        }
        
        public GUIStyle(GUIStyle other)
        {
            if (other == null) return;
            
            BackgroundColor = other.BackgroundColor;
            BackgroundImage = other.BackgroundImage;
            BackgroundImageScale = other.BackgroundImageScale;
            BorderColor = other.BorderColor;
            BorderWidth = other.BorderWidth;
            BorderRadius = other.BorderRadius;
            TextColor = other.TextColor;
            FontFamily = other.FontFamily;
            FontSize = other.FontSize;
            FontStyle = other.FontStyle;
            TextAlignment = other.TextAlignment;
            TextVerticalAlignment = other.TextVerticalAlignment;
            Padding = new Padding(other.Padding);
            Margin = new Padding(other.Margin);
            ShadowColor = other.ShadowColor;
            ShadowOffset = other.ShadowOffset;
            ShadowBlur = other.ShadowBlur;
            TransitionDuration = other.TransitionDuration;
            TransitionEasing = other.TransitionEasing;
        }
        
        public GUIStyle GetStateStyle(ElementState state)
        {
            return state switch
            {
                ElementState.Hover => HoverStyle ?? this,
                ElementState.Pressed => PressedStyle ?? this,
                ElementState.Disabled => DisabledStyle ?? this,
                ElementState.Focused => FocusedStyle ?? this,
                _ => this
            };
        }
    }
    
    [Serializable]
    public struct Padding
    {
        public float Left;
        public float Top;
        public float Right;
        public float Bottom;
        
        public Padding(float all)
        {
            Left = Top = Right = Bottom = all;
        }
        
        public Padding(float horizontal, float vertical)
        {
            Left = Right = horizontal;
            Top = Bottom = vertical;
        }
        
        public Padding(float left, float top, float right, float bottom)
        {
            Left = left;
            Top = top;
            Right = right;
            Bottom = bottom;
        }
        
        public Padding(Padding other)
        {
            Left = other.Left;
            Top = other.Top;
            Right = other.Right;
            Bottom = other.Bottom;
        }
        
        public Vector2 Horizontal => new Vector2(Left, Right);
        public Vector2 Vertical => new Vector2(Top, Bottom);
        public Vector2 Total => new Vector2(Left + Right, Top + Bottom);
    }
    
    public enum FontStyle
    {
        Normal,
        Bold,
        Italic,
        BoldItalic
    }
    
    public enum TextAlignment
    {
        Left,
        Center,
        Right
    }
    
    public enum TextVerticalAlignment
    {
        Top,
        Middle,
        Bottom
    }
    
    public enum EasingType
    {
        Linear,
        EaseIn,
        EaseOut,
        EaseInOut
    }
    
    public enum ElementState
    {
        Normal,
        Hover,
        Pressed,
        Disabled,
        Focused
    }
}
