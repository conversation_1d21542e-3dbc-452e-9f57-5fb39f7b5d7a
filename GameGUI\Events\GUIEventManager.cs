using GameGUI.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GameGUI.Events
{
    /// <summary>
    /// Manages GUI events and input handling
    /// </summary>
    public class GUIEventManager
    {
        private IGUIElement _rootElement;
        private IGUIElement _focusedElement;
        private IGUIElement _hoveredElement;
        private IGUIElement _pressedElement;
        private Vector2 _lastMousePosition;
        private readonly Dictionary<KeyCode, bool> _keyStates = new Dictionary<KeyCode, bool>();
        
        public IGUIElement RootElement
        {
            get => _rootElement;
            set => _rootElement = value;
        }
        
        public IGUIElement FocusedElement
        {
            get => _focusedElement;
            private set
            {
                if (_focusedElement != value)
                {
                    var oldFocused = _focusedElement;
                    _focusedElement = value;
                    
                    oldFocused?.TriggerBlur();
                    _focusedElement?.TriggerFocus();
                }
            }
        }
        
        public IGUIElement HoveredElement
        {
            get => _hoveredElement;
            private set
            {
                if (_hoveredElement != value)
                {
                    var oldHovered = _hoveredElement;
                    _hoveredElement = value;
                    
                    if (oldHovered != null)
                        OnMouseLeave(oldHovered);
                    if (_hoveredElement != null)
                        OnMouseEnter(_hoveredElement);
                }
            }
        }
        
        public Vector2 MousePosition => _lastMousePosition;
        
        public event Action<IGUIElement, GUIInputEventArgs> OnElementClicked;
        public event Action<IGUIElement, GUIInputEventArgs> OnElementHovered;
        public event Action<IGUIElement, GUIInputEventArgs> OnElementFocused;
        public event Action<IGUIElement, GUIInputEventArgs> OnElementBlurred;
        
        public GUIEventManager()
        {
        }
        
        public GUIEventManager(IGUIElement rootElement)
        {
            _rootElement = rootElement;
        }
        
        public void HandleMouseMove(Vector2 position)
        {
            _lastMousePosition = position;
            
            var hitElement = GetElementAtPosition(position);
            HoveredElement = hitElement;
        }
        
        public void HandleMouseDown(Vector2 position, MouseButton button)
        {
            _lastMousePosition = position;
            
            var hitElement = GetElementAtPosition(position);
            
            if (hitElement != null && hitElement.Interactive && hitElement.Enabled)
            {
                _pressedElement = hitElement;
                FocusedElement = hitElement;
                
                // Handle specific element types
                if (hitElement is Elements.Button btn)
                {
                    btn.HandleMouseDown(position);
                }
                else if (hitElement is Elements.TextBox textBox)
                {
                    textBox.HandleMouseDown(position);
                }
                
                var eventArgs = new GUIInputEventArgs
                {
                    Position = position,
                    Button = button
                };
                
                OnElementClicked?.Invoke(hitElement, eventArgs);
                hitElement.TriggerClick();
            }
            else
            {
                // Clicked on empty space, clear focus
                FocusedElement = null;
                _pressedElement = null;
            }
        }
        
        public void HandleMouseUp(Vector2 position, MouseButton button)
        {
            _lastMousePosition = position;
            
            if (_pressedElement != null)
            {
                // Handle specific element types
                if (_pressedElement is Elements.Button btn)
                {
                    btn.HandleMouseUp(position);
                }
                
                _pressedElement = null;
            }
        }
        
        public void HandleKeyDown(KeyCode key)
        {
            _keyStates[key] = true;
            
            if (FocusedElement != null && FocusedElement.Interactive && FocusedElement.Enabled)
            {
                // Handle specific element types
                if (FocusedElement is Elements.TextBox textBox)
                {
                    textBox.HandleKeyInput(key, '\0');
                }
            }
        }
        
        public void HandleKeyUp(KeyCode key)
        {
            _keyStates[key] = false;
        }
        
        public void HandleCharacterInput(char character)
        {
            if (FocusedElement != null && FocusedElement.Interactive && FocusedElement.Enabled)
            {
                // Handle specific element types
                if (FocusedElement is Elements.TextBox textBox)
                {
                    textBox.HandleKeyInput(KeyCode.None, character);
                }
            }
        }
        
        public bool IsKeyPressed(KeyCode key)
        {
            return _keyStates.TryGetValue(key, out bool pressed) && pressed;
        }
        
        public void SetFocus(IGUIElement element)
        {
            if (element == null || !element.Interactive || !element.Enabled)
            {
                FocusedElement = null;
                return;
            }
            
            FocusedElement = element;
        }
        
        public void ClearFocus()
        {
            FocusedElement = null;
        }
        
        private IGUIElement GetElementAtPosition(Vector2 position)
        {
            if (_rootElement == null)
                return null;
                
            return GetElementAtPositionRecursive(_rootElement, position);
        }
        
        private IGUIElement GetElementAtPositionRecursive(IGUIElement element, Vector2 position)
        {
            if (!element.Visible || !element.ContainsPoint(position))
                return null;
            
            // Check children first (they render on top)
            var sortedChildren = element.Children.OrderByDescending(c => c.ZOrder);
            foreach (var child in sortedChildren)
            {
                var hitChild = GetElementAtPositionRecursive(child, position);
                if (hitChild != null)
                    return hitChild;
            }
            
            // If no child was hit and this element is interactive, return it
            if (element.Interactive)
                return element;
                
            return null;
        }
        
        private void OnMouseEnter(IGUIElement element)
        {
            if (element is Elements.Button btn)
            {
                btn.HandleMouseEnter(_lastMousePosition);
            }
            
            var eventArgs = new GUIInputEventArgs
            {
                Position = _lastMousePosition
            };
            
            OnElementHovered?.Invoke(element, eventArgs);
            element.TriggerHover();
        }
        
        private void OnMouseLeave(IGUIElement element)
        {
            if (element is Elements.Button btn)
            {
                btn.HandleMouseLeave();
            }
        }
        
        public void Update(float deltaTime)
        {
            // Update any time-based event handling here
            // For example, double-click detection, drag operations, etc.
        }
    }
}
