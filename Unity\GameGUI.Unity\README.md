# GameGUI for Unity

A powerful, flexible GUI system built in pure C# for Unity game development.

## Installation

### Via Unity Package Manager

1. Copy this folder to your Unity project's `Packages` directory
2. Unity will automatically detect and import the package

### Manual Installation

1. Import the `GameGUI.Core.dll` into your project's `Assets/Plugins` folder
2. Copy the Runtime scripts to your project
3. Copy the Samples if desired

## Quick Start

1. Create an empty GameObject in your scene
2. Add the `UnityGUIBridge` component to it
3. Create a script to build your UI:

```csharp
using GameGUI.Unity;
using UnityEngine;

public class MyGameGUI : MonoBehaviour
{
    private UnityGUIBridge guiBridge;
    
    void Start()
    {
        guiBridge = GetComponent<UnityGUIBridge>();
        CreateUI();
    }
    
    void CreateUI()
    {
        // Create a simple menu
        var menu = UnityGUIHelper.CreateSimpleMenu("Main Menu",
            ("Start Game", StartGame),
            ("Options", ShowOptions),
            ("Quit", QuitGame)
        );
        
        guiBridge.AddElement(menu);
    }
    
    void StartGame() => Debug.Log("Starting game...");
    void ShowOptions() => Debug.Log("Showing options...");
    void QuitGame() => Application.Quit();
}
```

## Components

### UnityGUIBridge

The main component that bridges the pure C# GameGUI system with Unity.

**Key Properties:**
- `Enable GUI`: Toggle GUI rendering
- `Target Camera`: Camera for coordinate conversion (defaults to main camera)
- `Use Screen Space`: Whether to use screen space coordinates
- `Reference Resolution`: Base resolution for scaling
- `Scale With Screen Size`: Whether to scale GUI with screen size

### UnityRenderer

Implements the IGUIRenderer interface using Unity's immediate mode GUI system.

### UnityGUIHelper

Utility class with helper methods for common Unity GUI tasks:
- `CreateButton()`: Quick button creation
- `CreateLabel()`: Quick label creation
- `CreatePanel()`: Quick panel creation
- `CreateSimpleMenu()`: Create a basic menu layout
- Coordinate conversion utilities

## Examples

### Basic Example

See `Samples~/BasicExamples/Scripts/BasicGUIExample.cs` for a complete basic example showing:
- Button creation and interaction
- Text input handling
- Theme switching
- FPS display

### Advanced Example

See `Samples~/AdvancedExamples/Scripts/AdvancedGUIExample.cs` for advanced features:
- Custom elements (ProgressBar, Slider)
- Layout management
- Dynamic content creation
- Complex interactions

## Theming

GameGUI includes a comprehensive theming system:

```csharp
// Use built-in themes
var darkTheme = GUITheme.CreateDarkTheme();
var lightTheme = GUITheme.CreateLightTheme();

// Apply to elements
darkTheme.ApplyToElement(myButton);

// Create custom themes
var customTheme = new GUITheme("Custom")
{
    Colors = new ThemeColors
    {
        Primary = Color.FromHex("#FF6B6B"),
        Background = Color.FromHex("#2C3E50")
    }
};
```

## Custom Elements

Create custom GUI elements by inheriting from `GUIElement`:

```csharp
public class HealthBar : GUIElement
{
    public float Health { get; set; } = 1.0f;
    
    protected override void OnRender(IGUIRenderer renderer)
    {
        var bounds = GetBounds();
        
        // Draw background
        renderer.DrawRect(bounds, Style.BackgroundColor);
        
        // Draw health fill
        var healthWidth = bounds.Width * Health;
        var healthRect = new Rect(bounds.X, bounds.Y, healthWidth, bounds.Height);
        renderer.DrawRect(healthRect, Color.Red);
    }
}
```

## Performance Tips

- Use object pooling for frequently created/destroyed elements
- Minimize GUI updates per frame
- Use the built-in layout managers for automatic positioning
- Consider element visibility to avoid unnecessary rendering

## Troubleshooting

**GUI not appearing:**
- Ensure UnityGUIBridge is enabled
- Check that elements are added to the root or a parent element
- Verify camera reference is set correctly

**Input not working:**
- Ensure Interactive property is true on elements
- Check that elements are not overlapped by others
- Verify event handlers are properly attached

**Scaling issues:**
- Adjust Reference Resolution in UnityGUIBridge
- Enable/disable Scale With Screen Size as needed
- Use UnityGUIHelper.GetScreenRelativePosition() for responsive layouts

## API Reference

### Core Classes

- `IGUIElement`: Base interface for all GUI elements
- `GUIElement`: Base implementation of IGUIElement
- `GUIManager`: Core manager for the GUI system
- `GUIStyle`: Styling properties for elements
- `GUITheme`: Complete theme with element styles

### Elements

- `Panel`: Container element
- `Button`: Interactive button
- `Label`: Text display
- `TextBox`: Text input
- `Image`: Image display

### Layout

- `StackLayout`: Vertical/horizontal stacking
- `GridLayout`: Grid-based layout
- `ILayoutManager`: Interface for custom layouts

## Support

For issues, questions, or contributions, please visit the project repository.
