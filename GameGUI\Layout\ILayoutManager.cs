using GameGUI.Core;
using System.Collections.Generic;

namespace GameGUI.Layout
{
    /// <summary>
    /// Interface for layout managers that automatically position and size GUI elements
    /// </summary>
    public interface ILayoutManager
    {
        /// <summary>
        /// Calculate and apply layout for the given container and its children
        /// </summary>
        /// <param name="container">The container element</param>
        /// <param name="children">The child elements to layout</param>
        void CalculateLayout(IGUIElement container, IList<IGUIElement> children);
        
        /// <summary>
        /// Get the preferred size for the container based on its children
        /// </summary>
        /// <param name="container">The container element</param>
        /// <param name="children">The child elements</param>
        /// <returns>The preferred size</returns>
        Vector2 GetPreferredSize(IGUIElement container, IList<IGUIElement> children);
    }
    
    /// <summary>
    /// Base class for layout managers
    /// </summary>
    public abstract class LayoutManager : ILayoutManager
    {
        public abstract void CalculateLayout(IGUIElement container, IList<IGUIElement> children);
        
        public virtual Vector2 GetPreferredSize(IGUIElement container, IList<IGUIElement> children)
        {
            // Default implementation: return current size
            return container.Size;
        }
        
        protected Rect GetContentBounds(IGUIElement container)
        {
            var bounds = container.GetBounds();
            var style = container.Style;
            
            return new Rect(
                bounds.X + style.Padding.Left,
                bounds.Y + style.Padding.Top,
                bounds.Width - style.Padding.Left - style.Padding.Right,
                bounds.Height - style.Padding.Top - style.Padding.Bottom
            );
        }
        
        protected Vector2 GetElementPreferredSize(IGUIElement element)
        {
            // This could be enhanced to query the element for its preferred size
            return element.Size;
        }
    }
}
