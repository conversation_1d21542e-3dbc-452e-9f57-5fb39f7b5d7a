using UnityEngine;
using GameGUI.Core;
using GameGUI.Elements;
using GameGUI.Unity.Editor;

namespace GameGUI.Unity
{
    /// <summary>
    /// Runtime component to load GUI designs from assets
    /// </summary>
    public class GUIDesignLoader : MonoBehaviour
    {
        [Header("Design Settings")]
        [SerializeField] private GUIDesignAsset designAsset;
        [SerializeField] private bool loadOnStart = true;
        
        private UnityGUIBridge guiBridge;
        
        void Start()
        {
            if (loadOnStart && designAsset != null)
            {
                LoadDesign();
            }
        }
        
        public void LoadDesign()
        {
            if (designAsset == null)
            {
                Debug.LogError("No design asset assigned!");
                return;
            }
            
            // Get or create GUI bridge
            guiBridge = GetComponent<UnityGUIBridge>();
            if (guiBridge == null)
            {
                guiBridge = gameObject.AddComponent<UnityGUIBridge>();
            }
            
            // Set reference resolution
            guiBridge.SetReferenceResolution(new Vector2(
                designAsset.referenceResolution.x,
                designAsset.referenceResolution.y
            ));
            
            // Load all root elements
            foreach (var elementData in designAsset.rootElements)
            {
                var element = CreateElementFromData(elementData);
                if (element != null)
                {
                    guiBridge.AddElement(element);
                }
            }
            
            Debug.Log($"Loaded GUI design: {designAsset.designName}");
        }
        
        private IGUIElement CreateElementFromData(GUIElementData data)
        {
            IGUIElement element = null;
            
            // Create element based on type
            switch (data.elementType)
            {
                case "Panel":
                    element = new Panel();
                    break;
                    
                case "Button":
                    var button = new Button(data.text);
                    // You can add event handlers here if needed
                    element = button;
                    break;
                    
                case "Label":
                    element = new Label(data.text);
                    break;
                    
                case "TextBox":
                    var textBox = new TextBox(data.placeholder);
                    textBox.IsPassword = data.isPassword;
                    element = textBox;
                    break;
                    
                case "Image":
                    var image = new Image(data.imagePath);
                    element = image;
                    break;
                    
                default:
                    Debug.LogWarning($"Unknown element type: {data.elementType}");
                    return null;
            }
            
            // Set common properties
            element.Name = data.name;
            element.Position = data.position;
            element.Size = data.size;
            element.Visible = data.visible;
            element.Enabled = data.enabled;
            element.Interactive = data.interactive;
            
            // Set style
            element.Style = new GUIStyle
            {
                BackgroundColor = data.backgroundColor,
                TextColor = data.textColor,
                BorderColor = data.borderColor,
                BorderWidth = data.borderWidth,
                FontSize = data.fontSize
            };
            
            // Parse text alignment
            if (!string.IsNullOrEmpty(data.textAlignment))
            {
                if (System.Enum.TryParse<TextAlignment>(data.textAlignment, out var alignment))
                {
                    element.Style.TextAlignment = alignment;
                }
            }
            
            // Create children
            foreach (var childData in data.children)
            {
                var child = CreateElementFromData(childData);
                if (child != null)
                {
                    element.AddChild(child);
                }
            }
            
            return element;
        }
        
        public void SetDesign(GUIDesignAsset design)
        {
            designAsset = design;
        }
        
        public GUIDesignAsset GetDesign()
        {
            return designAsset;
        }
    }
}
