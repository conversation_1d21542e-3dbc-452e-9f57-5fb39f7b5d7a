using GameGUI.Core;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace GameGUI.Unity
{
    /// <summary>
    /// Modern Canvas-based renderer for GameGUI using Unity UI system
    /// Provides high-performance, GPU-accelerated rendering with full Unity UI integration
    /// </summary>
    public class UnityCanvasRenderer : IGUIRenderer
    {
        private Canvas _canvas;
        private RectTransform _canvasTransform;
        private CanvasScaler _canvasScaler;
        private GraphicRaycaster _raycaster;
        
        private readonly Dictionary<string, Texture2D> _textureCache = new Dictionary<string, Texture2D>();
        private readonly Dictionary<string, Font> _fontCache = new Dictionary<string, Font>();
        private readonly Dictionary<string, GameObject> _elementCache = new Dictionary<string, GameObject>();
        private readonly Stack<RectTransform> _transformStack = new Stack<RectTransform>();
        private readonly Stack<RectMask2D> _clipStack = new Stack<RectMask2D>();
        
        private GameObject _rootObject;
        private RectTransform _currentParent;
        
        public Canvas Canvas => _canvas;
        public RectTransform CanvasTransform => _canvasTransform;
        
        public UnityCanvasRenderer(Canvas canvas = null)
        {
            if (canvas != null)
            {
                _canvas = canvas;
                _canvasTransform = canvas.GetComponent<RectTransform>();
            }
            else
            {
                CreateCanvas();
            }
            
            _currentParent = _canvasTransform;
        }
        
        private void CreateCanvas()
        {
            // Create canvas GameObject
            _rootObject = new GameObject("GameGUI Canvas");
            _canvas = _rootObject.AddComponent<Canvas>();
            _canvasTransform = _rootObject.GetComponent<RectTransform>();
            
            // Configure canvas for screen space overlay (best for UI)
            _canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            _canvas.sortingOrder = 100; // Render on top
            
            // Add canvas scaler for resolution independence
            _canvasScaler = _rootObject.AddComponent<CanvasScaler>();
            _canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            _canvasScaler.referenceResolution = new Vector2(1920, 1080);
            _canvasScaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            _canvasScaler.matchWidthOrHeight = 0.5f;
            
            // Add graphic raycaster for input
            _raycaster = _rootObject.AddComponent<GraphicRaycaster>();
            
            // Ensure canvas persists across scenes if needed
            Object.DontDestroyOnLoad(_rootObject);
        }
        
        public void SetRenderMode(RenderMode mode, Camera camera = null)
        {
            _canvas.renderMode = mode;
            if (mode == RenderMode.ScreenSpaceCamera || mode == RenderMode.WorldSpace)
            {
                _canvas.worldCamera = camera ?? Camera.main;
            }
        }
        
        public void SetReferenceResolution(Vector2 resolution)
        {
            if (_canvasScaler != null)
            {
                _canvasScaler.referenceResolution = new UnityEngine.Vector2(resolution.X, resolution.Y);
            }
        }
        
        public void BeginRender()
        {
            _transformStack.Clear();
            _clipStack.Clear();
            _currentParent = _canvasTransform;
        }
        
        public void EndRender()
        {
            // Cleanup any temporary objects
            while (_transformStack.Count > 0)
            {
                _transformStack.Pop();
            }
            while (_clipStack.Count > 0)
            {
                _clipStack.Pop();
            }
        }
        
        public void SetClipRect(Rect clipRect)
        {
            // Create a clipping container using RectMask2D
            var clipObj = new GameObject("ClipRect");
            clipObj.transform.SetParent(_currentParent, false);
            
            var rectTransform = clipObj.AddComponent<RectTransform>();
            rectTransform.anchorMin = UnityEngine.Vector2.zero;
            rectTransform.anchorMax = UnityEngine.Vector2.zero;
            rectTransform.pivot = UnityEngine.Vector2.zero;
            rectTransform.anchoredPosition = new UnityEngine.Vector2(clipRect.X, clipRect.Y);
            rectTransform.sizeDelta = new UnityEngine.Vector2(clipRect.Width, clipRect.Height);
            
            var mask = clipObj.AddComponent<RectMask2D>();
            _clipStack.Push(mask);
            
            _transformStack.Push(_currentParent);
            _currentParent = rectTransform;
        }
        
        public void ClearClipRect()
        {
            if (_clipStack.Count > 0)
            {
                _clipStack.Pop();
            }
            
            if (_transformStack.Count > 0)
            {
                _currentParent = _transformStack.Pop();
            }
        }
        
        public void DrawRect(Rect rect, GameGUI.Core.Color color)
        {
            var obj = CreateUIObject("Rect");
            var rectTransform = SetupRectTransform(obj, rect);
            
            var image = obj.AddComponent<Image>();
            image.color = ToUnityColor(color);
            image.raycastTarget = false; // Optimize - don't raycast background elements
        }
        
        public void DrawRectOutline(Rect rect, GameGUI.Core.Color color, float lineWidth)
        {
            var obj = CreateUIObject("RectOutline");
            var rectTransform = SetupRectTransform(obj, rect);
            
            var outline = obj.AddComponent<Outline>();
            outline.effectColor = ToUnityColor(color);
            outline.effectDistance = new UnityEngine.Vector2(lineWidth, lineWidth);
            
            // Add transparent image for outline to work
            var image = obj.AddComponent<Image>();
            image.color = new UnityEngine.Color(0, 0, 0, 0);
            image.raycastTarget = false;
        }
        
        public void DrawRoundedRect(Rect rect, float radius, GameGUI.Core.Color color)
        {
            // For rounded rectangles, we'll use a sprite with rounded corners
            var obj = CreateUIObject("RoundedRect");
            var rectTransform = SetupRectTransform(obj, rect);
            
            var image = obj.AddComponent<Image>();
            image.color = ToUnityColor(color);
            image.type = Image.Type.Sliced;
            image.raycastTarget = false;
            
            // You can set a rounded sprite here if available
            // image.sprite = LoadRoundedSprite(radius);
        }
        
        public void DrawRoundedRectOutline(Rect rect, float radius, GameGUI.Core.Color color, float lineWidth)
        {
            DrawRoundedRect(rect, radius, color);
            DrawRectOutline(rect, color, lineWidth);
        }
        
        public void DrawCircle(Vector2 center, float radius, GameGUI.Core.Color color)
        {
            var obj = CreateUIObject("Circle");
            var rectTransform = obj.GetComponent<RectTransform>();
            
            rectTransform.anchorMin = UnityEngine.Vector2.zero;
            rectTransform.anchorMax = UnityEngine.Vector2.zero;
            rectTransform.pivot = new UnityEngine.Vector2(0.5f, 0.5f);
            rectTransform.anchoredPosition = new UnityEngine.Vector2(center.X, center.Y);
            rectTransform.sizeDelta = new UnityEngine.Vector2(radius * 2, radius * 2);
            
            var image = obj.AddComponent<Image>();
            image.color = ToUnityColor(color);
            image.type = Image.Type.Filled;
            image.fillMethod = Image.FillMethod.Radial360;
            image.raycastTarget = false;
            
            // Use a circle sprite if available
            // image.sprite = LoadCircleSprite();
        }
        
        public void DrawCircleOutline(Vector2 center, float radius, GameGUI.Core.Color color, float lineWidth)
        {
            DrawCircle(center, radius, new GameGUI.Core.Color(0, 0, 0, 0));
            
            var obj = _currentParent.GetChild(_currentParent.childCount - 1).gameObject;
            var outline = obj.AddComponent<Outline>();
            outline.effectColor = ToUnityColor(color);
            outline.effectDistance = new UnityEngine.Vector2(lineWidth, lineWidth);
        }
        
        public void DrawLine(Vector2 start, Vector2 end, GameGUI.Core.Color color, float lineWidth)
        {
            var direction = end - start;
            var length = direction.Magnitude;
            var angle = Mathf.Atan2(direction.Y, direction.X) * Mathf.Rad2Deg;
            
            var midPoint = new Vector2(
                (start.X + end.X) * 0.5f,
                (start.Y + end.Y) * 0.5f
            );
            
            var rect = new Rect(midPoint.X - length * 0.5f, midPoint.Y - lineWidth * 0.5f, length, lineWidth);
            var obj = CreateUIObject("Line");
            var rectTransform = SetupRectTransform(obj, rect);
            rectTransform.rotation = Quaternion.Euler(0, 0, angle);
            
            var image = obj.AddComponent<Image>();
            image.color = ToUnityColor(color);
            image.raycastTarget = false;
        }
        
        public void DrawText(string text, Vector2 position, GUIStyle style)
        {
            if (string.IsNullOrEmpty(text))
                return;

            var obj = CreateUIObject("Text");
            var rectTransform = obj.GetComponent<RectTransform>();

            rectTransform.anchorMin = UnityEngine.Vector2.zero;
            rectTransform.anchorMax = UnityEngine.Vector2.zero;
            rectTransform.pivot = UnityEngine.Vector2.zero;
            rectTransform.anchoredPosition = new UnityEngine.Vector2(position.X, position.Y);

            var textComponent = obj.AddComponent<Text>();
            textComponent.text = text;
            textComponent.color = ToUnityColor(style.TextColor);
            textComponent.fontSize = (int)style.FontSize;
            textComponent.font = LoadFont(style.FontFamily, style.FontSize, style.FontStyle) as Font;
            textComponent.alignment = ConvertTextAlignment(style.TextAlignment);
            textComponent.fontStyle = ConvertFontStyle(style.FontStyle);
            textComponent.raycastTarget = false;

            // Auto-size the rect to fit text
            var preferredSize = textComponent.preferredWidth;
            var preferredHeight = textComponent.preferredHeight;
            rectTransform.sizeDelta = new UnityEngine.Vector2(preferredSize, preferredHeight);
        }

        public Vector2 MeasureText(string text, GUIStyle style)
        {
            if (string.IsNullOrEmpty(text))
                return Vector2.Zero;

            // Create temporary text component for measurement
            var tempObj = new GameObject("TempText");
            var textComponent = tempObj.AddComponent<Text>();
            textComponent.text = text;
            textComponent.fontSize = (int)style.FontSize;
            textComponent.font = LoadFont(style.FontFamily, style.FontSize, style.FontStyle) as Font;
            textComponent.fontStyle = ConvertFontStyle(style.FontStyle);

            var size = new Vector2(textComponent.preferredWidth, textComponent.preferredHeight);
            Object.Destroy(tempObj);

            return size;
        }

        public void DrawImage(string imagePath, Rect destRect, Rect? sourceRect = null, GameGUI.Core.Color? tint = null)
        {
            var texture = LoadTexture(imagePath) as Texture2D;
            if (texture != null)
            {
                DrawImage(texture, destRect, sourceRect, tint);
            }
        }

        public void DrawImage(object imageResource, Rect destRect, Rect? sourceRect = null, GameGUI.Core.Color? tint = null)
        {
            if (imageResource is Texture2D texture)
            {
                var obj = CreateUIObject("Image");
                var rectTransform = SetupRectTransform(obj, destRect);

                var image = obj.AddComponent<RawImage>();
                image.texture = texture;
                image.color = tint.HasValue ? ToUnityColor(tint.Value) : UnityEngine.Color.white;
                image.raycastTarget = false;

                if (sourceRect.HasValue)
                {
                    var src = sourceRect.Value;
                    image.uvRect = new UnityEngine.Rect(
                        src.X / texture.width,
                        src.Y / texture.height,
                        src.Width / texture.width,
                        src.Height / texture.height
                    );
                }
            }
            else if (imageResource is Sprite sprite)
            {
                var obj = CreateUIObject("Image");
                var rectTransform = SetupRectTransform(obj, destRect);

                var image = obj.AddComponent<Image>();
                image.sprite = sprite;
                image.color = tint.HasValue ? ToUnityColor(tint.Value) : UnityEngine.Color.white;
                image.raycastTarget = false;
            }
        }

        public void DrawGradient(Rect rect, GameGUI.Core.Color topLeft, GameGUI.Core.Color topRight,
            GameGUI.Core.Color bottomLeft, GameGUI.Core.Color bottomRight)
        {
            var obj = CreateUIObject("Gradient");
            var rectTransform = SetupRectTransform(obj, rect);

            var image = obj.AddComponent<Image>();
            image.raycastTarget = false;

            // Add gradient component (custom component needed)
            var gradient = obj.AddComponent<UIGradient>();
            gradient.topLeft = ToUnityColor(topLeft);
            gradient.topRight = ToUnityColor(topRight);
            gradient.bottomLeft = ToUnityColor(bottomLeft);
            gradient.bottomRight = ToUnityColor(bottomRight);
        }

        public void DrawShadow(Rect rect, Vector2 offset, float blur, GameGUI.Core.Color color)
        {
            var obj = CreateUIObject("Shadow");
            var rectTransform = SetupRectTransform(obj, rect);

            var shadow = obj.AddComponent<Shadow>();
            shadow.effectColor = ToUnityColor(color);
            shadow.effectDistance = new UnityEngine.Vector2(offset.X, offset.Y);
            shadow.useGraphicAlpha = true;

            // Add image for shadow to work
            var image = obj.AddComponent<Image>();
            image.color = new UnityEngine.Color(0, 0, 0, 0);
            image.raycastTarget = false;
        }

        public void PushTransform(Vector2 translation, float rotation, Vector2 scale)
        {
            var obj = CreateUIObject("Transform");
            var rectTransform = obj.GetComponent<RectTransform>();

            rectTransform.anchoredPosition = new UnityEngine.Vector2(translation.X, translation.Y);
            rectTransform.rotation = Quaternion.Euler(0, 0, rotation);
            rectTransform.localScale = new UnityEngine.Vector3(scale.X, scale.Y, 1);

            _transformStack.Push(_currentParent);
            _currentParent = rectTransform;
        }

        public void PopTransform()
        {
            if (_transformStack.Count > 0)
            {
                _currentParent = _transformStack.Pop();
            }
        }

        public object LoadTexture(string path)
        {
            if (_textureCache.TryGetValue(path, out var cachedTexture))
                return cachedTexture;

            var texture = Resources.Load<Texture2D>(path);
            if (texture != null)
            {
                _textureCache[path] = texture;
            }

            return texture;
        }

        public void UnloadTexture(object texture)
        {
            if (texture is Texture2D tex)
            {
                var pathToRemove = string.Empty;
                foreach (var kvp in _textureCache)
                {
                    if (kvp.Value == tex)
                    {
                        pathToRemove = kvp.Key;
                        break;
                    }
                }

                if (!string.IsNullOrEmpty(pathToRemove))
                {
                    _textureCache.Remove(pathToRemove);
                }
            }
        }

        public object LoadFont(string fontFamily, float fontSize, FontStyle fontStyle)
        {
            var key = $"{fontFamily}_{fontSize}_{fontStyle}";
            if (_fontCache.TryGetValue(key, out var cachedFont))
                return cachedFont;

            var font = Resources.Load<Font>(fontFamily);
            if (font == null)
            {
                font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            }

            if (font != null)
            {
                _fontCache[key] = font;
            }

            return font;
        }

        public void UnloadFont(object font)
        {
            if (font is Font f)
            {
                var keyToRemove = string.Empty;
                foreach (var kvp in _fontCache)
                {
                    if (kvp.Value == f)
                    {
                        keyToRemove = kvp.Key;
                        break;
                    }
                }

                if (!string.IsNullOrEmpty(keyToRemove))
                {
                    _fontCache.Remove(keyToRemove);
                }
            }
        }

        public Vector2 GetScreenSize()
        {
            return new Vector2(Screen.width, Screen.height);
        }

        public float GetDPIScale()
        {
            return Screen.dpi / 96f;
        }

        // Helper methods
        private GameObject CreateUIObject(string name)
        {
            var obj = new GameObject(name);
            obj.transform.SetParent(_currentParent, false);
            obj.AddComponent<RectTransform>();
            return obj;
        }

        private RectTransform SetupRectTransform(GameObject obj, Rect rect)
        {
            var rectTransform = obj.GetComponent<RectTransform>();
            rectTransform.anchorMin = UnityEngine.Vector2.zero;
            rectTransform.anchorMax = UnityEngine.Vector2.zero;
            rectTransform.pivot = UnityEngine.Vector2.zero;
            rectTransform.anchoredPosition = new UnityEngine.Vector2(rect.X, rect.Y);
            rectTransform.sizeDelta = new UnityEngine.Vector2(rect.Width, rect.Height);
            return rectTransform;
        }

        private UnityEngine.Color ToUnityColor(GameGUI.Core.Color color)
        {
            return new UnityEngine.Color(color.R, color.G, color.B, color.A);
        }

        private TextAnchor ConvertTextAlignment(TextAlignment alignment)
        {
            switch (alignment)
            {
                case TextAlignment.Left: return TextAnchor.MiddleLeft;
                case TextAlignment.Center: return TextAnchor.MiddleCenter;
                case TextAlignment.Right: return TextAnchor.MiddleRight;
                default: return TextAnchor.MiddleLeft;
            }
        }

        private UnityEngine.FontStyle ConvertFontStyle(FontStyle style)
        {
            switch (style)
            {
                case FontStyle.Bold: return UnityEngine.FontStyle.Bold;
                case FontStyle.Italic: return UnityEngine.FontStyle.Italic;
                case FontStyle.BoldItalic: return UnityEngine.FontStyle.BoldAndItalic;
                default: return UnityEngine.FontStyle.Normal;
            }
        }

        public void Cleanup()
        {
            // Clear caches
            _textureCache.Clear();
            _fontCache.Clear();
            _elementCache.Clear();

            // Destroy canvas if we created it
            if (_rootObject != null)
            {
                Object.Destroy(_rootObject);
            }
        }
    }
}

