﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>GameGUI.Core</id>
    <version>1.0.0</version>
    <authors>GameGUI Contributors</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <description>A pure C# GUI system designed for Unity integration. Core library with no Unity dependencies.</description>
    <tags>gui ui game unity gamedev csharp</tags>
    <repository type="git" url="https://github.com/yourusername/GameGUI" />
    <dependencies>
      <group targetFramework=".NETStandard2.1" />
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Documents\C#\bin\Release\netstandard2.1\GameGUI.Core.dll" target="lib\netstandard2.1\GameGUI.Core.dll" />
  </files>
</package>