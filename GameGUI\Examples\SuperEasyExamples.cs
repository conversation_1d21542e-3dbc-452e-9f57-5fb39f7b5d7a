using GameGUI.Core;
using GameGUI.Elements;
using System;

namespace GameGUI.Examples
{
    /// <summary>
    /// Super easy examples showing how simple it is to create UIs with GameGUI
    /// These examples demonstrate the fluent API that makes UI creation a breeze!
    /// </summary>
    public static class SuperEasyExamples
    {
        /// <summary>
        /// Example 1: Create a simple button in ONE line!
        /// </summary>
        public static Button CreateSimpleButton()
        {
            return UI.Button("Click Me!")
                .At(100, 100)
                .Size(150, 40)
                .OnClick(btn => Console.WriteLine("Button clicked!"));
        }
        
        /// <summary>
        /// Example 2: Create a beautiful login form in just a few lines!
        /// </summary>
        public static Panel CreateLoginForm()
        {
            return UI.VerticalLayout()
                .Padding(20)
                .Spacing(15)
                .Children(
                    UI.Label("Login")
                        .FontSize(24)
                        .TextColor(Color.FromHex("#333333")),
                    
                    UI.TextBox("Username")
                        .Width(300)
                        .Height(40),
                    
                    UI.TextBox("Password")
                        .Width(300)
                        .Height(40)
                        .Password(),
                    
                    UI.Toggle("Remember me"),
                    
                    <PERSON><PERSON>.<PERSON><PERSON>("Login")
                        .Width(300)
                        .Height(45)
                        .BackgroundColor(Color.FromHex("#2196F3"))
                        .TextColor(Color.White)
                        .BorderRadius(8)
                        .OnClick(btn => Console.WriteLine("Logging in..."))
                )
                .Build();
        }
        
        /// <summary>
        /// Example 3: Create a settings panel with sliders and toggles
        /// </summary>
        public static Panel CreateSettingsPanel()
        {
            return UI.VerticalLayout()
                .Padding(20)
                .Spacing(20)
                .Children(
                    UI.Label("Settings")
                        .FontSize(20)
                        .TextColor(Color.FromHex("#333333")),
                    
                    // Volume slider
                    UI.HorizontalLayout()
                        .Spacing(10)
                        .Children(
                            UI.Label("Volume:").Width(100),
                            UI.Slider(0, 100, 75)
                                .Width(200)
                                .OnValueChanged((slider, value) => 
                                    Console.WriteLine($"Volume: {value}%"))
                        ),
                    
                    // Graphics quality dropdown
                    UI.HorizontalLayout()
                        .Spacing(10)
                        .Children(
                            UI.Label("Quality:").Width(100),
                            UI.Dropdown("Low", "Medium", "High", "Ultra")
                                .Width(200)
                                .SelectedIndex(2)
                                .OnSelectionChanged((dd, index) => 
                                    Console.WriteLine($"Quality: {dd.SelectedOption}"))
                        ),
                    
                    // Toggles
                    UI.Toggle("Enable VSync", true)
                        .OnValueChanged((toggle, isOn) => 
                            Console.WriteLine($"VSync: {isOn}")),
                    
                    UI.Toggle("Fullscreen", false)
                        .AsSwitch()
                        .OnValueChanged((toggle, isOn) => 
                            Console.WriteLine($"Fullscreen: {isOn}")),
                    
                    // Save button
                    UI.Button("Save Settings")
                        .Width(200)
                        .BackgroundColor(Color.FromHex("#4CAF50"))
                        .TextColor(Color.White)
                        .OnClick(btn => Console.WriteLine("Settings saved!"))
                )
                .Build();
        }
        
        /// <summary>
        /// Example 4: Create a progress indicator
        /// </summary>
        public static Panel CreateProgressPanel()
        {
            var progressBar = UI.ProgressBar(0, 100, 0)
                .Width(400)
                .Height(30)
                .ShowText()
                .Build();

            return UI.VerticalLayout()
                .Padding(20)
                .Spacing(15)
                .Children(
                    UI.Label("Loading...").FontSize(18),
                    progressBar,
                    UI.Button("Start")
                        .OnClick(btn => SimulateProgress(progressBar))
                )
                .Build();
        }

        private static async System.Threading.Tasks.Task SimulateProgress(Elements.ProgressBar bar)
        {
            for (int i = 0; i <= 100; i++)
            {
                bar.Value = i;
                await System.Threading.Tasks.Task.Delay(50);
            }
        }
        
        /// <summary>
        /// Example 5: Create a scrollable list
        /// </summary>
        public static ScrollView CreateScrollableList()
        {
            var scrollView = UI.ScrollView()
                .Size(400, 300)
                .ContentSize(400, 1000)
                .Build();
            
            // Add 50 items to the list
            for (int i = 0; i < 50; i++)
            {
                int index = i;
                var item = UI.Button($"Item {i + 1}")
                    .At(10, i * 45 + 10)
                    .Size(380, 40)
                    .OnClick(btn => Console.WriteLine($"Clicked item {index + 1}"))
                    .Build();
                
                scrollView.AddContent(item);
            }
            
            return scrollView;
        }
        
        /// <summary>
        /// Example 6: Create a grid of buttons
        /// </summary>
        public static Panel CreateButtonGrid()
        {
            var buttons = new object[12];

            for (int i = 0; i < 12; i++)
            {
                int index = i;
                buttons[i] = UI.Button($"Btn {i + 1}")
                    .OnClick(btn => Console.WriteLine($"Button {index + 1} clicked"));
            }

            return UI.GridLayout(4)  // 4 columns
                .CellSize(80, 80)
                .Spacing(10)
                .Padding(20)
                .Children(buttons)
                .Build();
        }
        
        /// <summary>
        /// Example 7: Create a complete game HUD
        /// </summary>
        public static Panel CreateGameHUD()
        {
            // Health bar
            var healthBar = UI.ProgressBar(0, 100, 75)
                .Width(200)
                .Height(25)
                .CustomText("Health")
                .Build();
            healthBar.SetColor(Color.FromHex("#F44336"));
            
            // Mana bar
            var manaBar = UI.ProgressBar(0, 100, 50)
                .Width(200)
                .Height(25)
                .CustomText("Mana")
                .Build();
            manaBar.SetColor(Color.FromHex("#2196F3"));
            
            // Score label
            var scoreLabel = UI.Label("Score: 0")
                .FontSize(20)
                .TextColor(Color.White)
                .Build();
            
            return UI.Panel()
                .Size(1920, 1080)
                .BackgroundColor(new Color(0, 0, 0, 0))  // Transparent
                .Children(
                    // Top-left stats
                    UI.VerticalLayout()
                        .At(20, 20)
                        .Spacing(10)
                        .Children(healthBar, manaBar, scoreLabel)
                        .Build(),
                    
                    // Top-right mini-map placeholder
                    UI.Panel()
                        .At(1920 - 220, 20)
                        .Size(200, 200)
                        .BackgroundColor(Color.FromHex("#000000"))
                        .Border(Color.White, 2)
                        .Build(),
                    
                    // Bottom-center action bar
                    UI.HorizontalLayout()
                        .At(1920/2 - 250, 1080 - 100)
                        .Spacing(10)
                        .Children(
                            UI.Button("1").Size(80, 80).Build(),
                            UI.Button("2").Size(80, 80).Build(),
                            UI.Button("3").Size(80, 80).Build(),
                            UI.Button("4").Size(80, 80).Build(),
                            UI.Button("5").Size(80, 80).Build()
                        )
                        .Build()
                )
                .Build();
        }
        
        /// <summary>
        /// Example 8: Create a dialog box
        /// </summary>
        public static Panel CreateDialog(string title, string message, Action onConfirm, Action onCancel)
        {
            return UI.Panel()
                .Size(400, 200)
                .BackgroundColor(Color.White)
                .Border(Color.FromHex("#CCCCCC"), 2)
                .BorderRadius(10)
                .Children(
                    UI.Label(title)
                        .At(20, 20)
                        .FontSize(18)
                        .TextColor(Color.FromHex("#333333"))
                        .Build(),

                    UI.Label(message)
                        .At(20, 60)
                        .Width(360)
                        .TextColor(Color.FromHex("#666666"))
                        .Build(),

                    UI.HorizontalLayout()
                        .At(200, 150)
                        .Spacing(10)
                        .Children(
                            UI.Button("Cancel")
                                .Size(90, 35)
                                .BackgroundColor(Color.FromHex("#CCCCCC"))
                                .OnClick(btn => onCancel?.Invoke())
                                .Build(),

                            UI.Button("Confirm")
                                .Size(90, 35)
                                .BackgroundColor(Color.FromHex("#2196F3"))
                                .TextColor(Color.White)
                                .OnClick(btn => onConfirm?.Invoke())
                                .Build()
                        )
                        .Build()
                )
                .Build();
        }
        
        /// <summary>
        /// Example 9: Create a color picker (simplified)
        /// </summary>
        public static Panel CreateColorPicker(Action<Core.Color> onColorChanged)
        {
            var redSlider = UI.Slider(0, 255, 128).Width(200).Build();
            var greenSlider = UI.Slider(0, 255, 128).Width(200).Build();
            var blueSlider = UI.Slider(0, 255, 128).Width(200).Build();
            var preview = UI.Panel().Size(100, 100).Build();

            Action updateColor = () =>
            {
                var color = new Core.Color(
                    redSlider.Value / 255f,
                    greenSlider.Value / 255f,
                    blueSlider.Value / 255f,
                    1f
                );
                preview.Style.BackgroundColor = color;
                onColorChanged?.Invoke(color);
            };

            redSlider.OnValueChanged += (s, v) => updateColor();
            greenSlider.OnValueChanged += (s, v) => updateColor();
            blueSlider.OnValueChanged += (s, v) => updateColor();

            updateColor(); // Initial color

            return UI.VerticalLayout()
                .Padding(20)
                .Spacing(15)
                .Children(
                    UI.Label("Color Picker").FontSize(18).Build(),
                    preview,
                    UI.HorizontalLayout().Spacing(10).Children(UI.Label("Red:").Width(50).Build(), redSlider).Build(),
                    UI.HorizontalLayout().Spacing(10).Children(UI.Label("Green:").Width(50).Build(), greenSlider).Build(),
                    UI.HorizontalLayout().Spacing(10).Children(UI.Label("Blue:").Width(50).Build(), blueSlider).Build()
                )
                .Build();
        }
    }
}

