using System;
using System.Collections.Generic;

namespace GameGUI.Core
{
    /// <summary>
    /// Core GUI manager that handles the GUI system without platform dependencies
    /// </summary>
    public class GUIManager
    {
        private IGUIElement _rootElement;
        private IGUIRenderer _renderer;
        private readonly List<IGUIElement> _allElements = new List<IGUIElement>();
        private bool _isInitialized = false;
        
        public IGUIElement RootElement
        {
            get => _rootElement;
            set
            {
                _rootElement = value;
                RebuildElementList();
            }
        }
        
        public IGUIRenderer Renderer
        {
            get => _renderer;
            set => _renderer = value;
        }
        
        public bool IsInitialized => _isInitialized;
        
        public event Action<IGUIElement> OnElementAdded;
        public event Action<IGUIElement> OnElementRemoved;
        
        public GUIManager()
        {
        }
        
        public GUIManager(IGUIRenderer renderer)
        {
            _renderer = renderer;
        }
        
        public void Initialize(IGUIRenderer? renderer = null)
        {
            if (renderer != null)
                _renderer = renderer;

            if (_renderer == null)
                throw new InvalidOperationException("Renderer must be set before initialization");

            if (_rootElement == null)
            {
                _rootElement = CreateDefaultRootElement();
            }

            _isInitialized = true;
        }
        
        public void Update(float deltaTime)
        {
            if (!_isInitialized || _rootElement == null)
                return;
                
            _rootElement.Update(deltaTime);
        }
        
        public void Render()
        {
            if (!_isInitialized || _rootElement == null || _renderer == null)
                return;
                
            _renderer.BeginRender();
            _rootElement.Render(_renderer);
            _renderer.EndRender();
        }
        
        public T CreateElement<T>() where T : IGUIElement, new()
        {
            var element = new T();
            RegisterElement(element);
            return element;
        }
        
        public void AddElement(IGUIElement element, IGUIElement? parent = null)
        {
            if (element == null)
                return;

            var targetParent = parent ?? _rootElement;
            targetParent?.AddChild(element);

            RegisterElement(element);
            OnElementAdded?.Invoke(element);
        }
        
        public void RemoveElement(IGUIElement element)
        {
            if (element?.Parent != null)
            {
                element.Parent.RemoveChild(element);
                UnregisterElement(element);
                OnElementRemoved?.Invoke(element);
            }
        }
        
        public IGUIElement FindElement(string name)
        {
            return FindElementRecursive(_rootElement, name);
        }
        
        public T FindElement<T>(string name) where T : class, IGUIElement
        {
            return FindElement(name) as T;
        }
        
        public IEnumerable<IGUIElement> GetAllElements()
        {
            return _allElements.AsReadOnly();
        }
        
        public IEnumerable<T> GetElementsOfType<T>() where T : class, IGUIElement
        {
            foreach (var element in _allElements)
            {
                if (element is T typedElement)
                    yield return typedElement;
            }
        }
        
        public void SetRootSize(Vector2 size)
        {
            if (_rootElement != null)
            {
                _rootElement.Size = size;
            }
        }
        
        public void CalculateLayout()
        {
            _rootElement?.CalculateLayout();
        }
        
        private IGUIElement CreateDefaultRootElement()
        {
            return new Elements.Panel
            {
                Name = "Root",
                Position = Vector2.Zero,
                Size = new Vector2(1920, 1080), // Default size
                Style = new GUIStyle
                {
                    BackgroundColor = Color.Transparent
                }
            };
        }
        
        private void RegisterElement(IGUIElement element)
        {
            if (element != null && !_allElements.Contains(element))
            {
                _allElements.Add(element);
            }
        }
        
        private void UnregisterElement(IGUIElement element)
        {
            if (element != null)
            {
                _allElements.Remove(element);
                
                // Also unregister all children
                foreach (var child in element.Children)
                {
                    UnregisterElement(child);
                }
            }
        }
        
        private void RebuildElementList()
        {
            _allElements.Clear();
            if (_rootElement != null)
            {
                AddElementToListRecursive(_rootElement);
            }
        }
        
        private void AddElementToListRecursive(IGUIElement element)
        {
            _allElements.Add(element);
            foreach (var child in element.Children)
            {
                AddElementToListRecursive(child);
            }
        }
        
        private IGUIElement FindElementRecursive(IGUIElement element, string name)
        {
            if (element == null)
                return null;
                
            if (element.Name == name)
                return element;
                
            foreach (var child in element.Children)
            {
                var found = FindElementRecursive(child, name);
                if (found != null)
                    return found;
            }
            
            return null;
        }
    }
}
