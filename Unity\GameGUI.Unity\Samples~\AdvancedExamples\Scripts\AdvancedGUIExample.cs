using GameGUI.Core;
using GameGUI.Elements;
using GameGUI.Layout;
using GameGUI.Theming;
using GameGUI.Unity;
using UnityEngine;
using System.Collections.Generic;

namespace GameGUI.Unity.Examples
{
    /// <summary>
    /// Advanced example showing custom elements, layouts, and complex interactions
    /// </summary>
    public class AdvancedGUIExample : MonoBehaviour
    {
        [Header("Advanced Settings")]
        [SerializeField] private bool _showLayoutDemo = true;
        [SerializeField] private bool _showCustomElements = true;
        [SerializeField] private bool _showAnimations = true;

        private UnityGUIBridge _guiBridge;
        private GUITheme _currentTheme;
        private ProgressBar _progressBar;
        private Slider _slider;
        private Panel _layoutDemoPanel;
        private List<Button> _dynamicButtons = new List<Button>();

        private void Start()
        {
            SetupGUI();
            CreateAdvancedUI();
        }

        private void SetupGUI()
        {
            _guiBridge = UnityGUIHelper.SetupGUIBridge(gameObject, new Vector2(1920, 1080));
            _currentTheme = GUITheme.CreateDarkTheme();
        }

        private void CreateAdvancedUI()
        {
            if (_showCustomElements)
                CreateCustomElementsDemo();

            if (_showLayoutDemo)
                CreateLayoutDemo();

            CreateDynamicContentDemo();
        }

        private void CreateCustomElementsDemo()
        {
            var customPanel = UnityGUIHelper.CreatePanel(
                new Vector2(50, 50),
                new Vector2(350, 200),
                new Color(0.15f, 0.15f, 0.2f, 0.95f)
            );
            customPanel.Style.BorderColor = GameGUI.Core.Color.FromHex("#4A90E2");
            customPanel.Style.BorderWidth = 2;
            customPanel.Style.BorderRadius = 10;
            customPanel.Style.Padding = new Padding(15);
            _guiBridge.AddElement(customPanel);

            // Title
            var title = UnityGUIHelper.CreateLabel(
                "Custom Elements Demo",
                Vector2.zero,
                new Vector2(320, 25)
            );
            title.Style.TextColor = GameGUI.Core.Color.White;
            title.Style.FontSize = 16;
            title.Style.FontStyle = FontStyle.Bold;
            title.Style.TextAlignment = TextAlignment.Center;
            _guiBridge.AddElement(title, customPanel);

            // Progress bar
            _progressBar = new ProgressBar
            {
                Position = new GameGUI.Core.Vector2(0, 40),
                Size = new GameGUI.Core.Vector2(320, 20),
                Value = 0.3f,
                Style = new GUIStyle
                {
                    BackgroundColor = GameGUI.Core.Color.FromHex("#2A2A2A"),
                    BorderColor = GameGUI.Core.Color.FromHex("#4CAF50"),
                    BorderWidth = 1
                }
            };
            _guiBridge.AddElement(_progressBar, customPanel);

            // Progress label
            var progressLabel = UnityGUIHelper.CreateLabel(
                "Progress: 30%",
                new Vector2(0, 70),
                new Vector2(320, 20)
            );
            progressLabel.Style.TextColor = GameGUI.Core.Color.FromHex("#CCCCCC");
            progressLabel.Name = "ProgressLabel";
            _guiBridge.AddElement(progressLabel, customPanel);

            // Slider
            _slider = new Slider
            {
                Position = new GameGUI.Core.Vector2(0, 100),
                Size = new GameGUI.Core.Vector2(320, 20),
                MinValue = 0,
                MaxValue = 100,
                Value = 30
            };
            _slider.OnValueChanged += OnSliderValueChanged;
            _guiBridge.AddElement(_slider, customPanel);

            // Slider label
            var sliderLabel = UnityGUIHelper.CreateLabel(
                "Drag slider to change progress",
                new Vector2(0, 130),
                new Vector2(320, 20)
            );
            sliderLabel.Style.TextColor = GameGUI.Core.Color.FromHex("#888888");
            sliderLabel.Style.FontSize = 10;
            _guiBridge.AddElement(sliderLabel, customPanel);
        }

        private void CreateLayoutDemo()
        {
            _layoutDemoPanel = UnityGUIHelper.CreatePanel(
                new Vector2(420, 50),
                new Vector2(300, 400),
                new Color(0.1f, 0.2f, 0.15f, 0.95f)
            );
            _layoutDemoPanel.Style.BorderColor = GameGUI.Core.Color.FromHex("#2ECC71");
            _layoutDemoPanel.Style.BorderWidth = 2;
            _layoutDemoPanel.Style.BorderRadius = 10;
            _layoutDemoPanel.Style.Padding = new Padding(15);
            _guiBridge.AddElement(_layoutDemoPanel);

            // Title
            var title = UnityGUIHelper.CreateLabel(
                "Layout Demo",
                Vector2.zero,
                new Vector2(270, 25)
            );
            title.Style.TextColor = GameGUI.Core.Color.White;
            title.Style.FontSize = 16;
            title.Style.FontStyle = FontStyle.Bold;
            title.Style.TextAlignment = TextAlignment.Center;
            _guiBridge.AddElement(title, _layoutDemoPanel);

            // Layout buttons
            var stackButton = UnityGUIHelper.CreateButton(
                "Stack Layout",
                new Vector2(0, 40),
                new Vector2(130, 30),
                () => ApplyStackLayout()
            );
            _currentTheme.ApplyToElement(stackButton);
            _guiBridge.AddElement(stackButton, _layoutDemoPanel);

            var gridButton = UnityGUIHelper.CreateButton(
                "Grid Layout",
                new Vector2(140, 40),
                new Vector2(130, 30),
                () => ApplyGridLayout()
            );
            _currentTheme.ApplyToElement(gridButton);
            _guiBridge.AddElement(gridButton, _layoutDemoPanel);

            // Create demo elements for layout
            CreateLayoutDemoElements();
        }

        private void CreateLayoutDemoElements()
        {
            var colors = new[]
            {
                GameGUI.Core.Color.FromHex("#E74C3C"),
                GameGUI.Core.Color.FromHex("#3498DB"),
                GameGUI.Core.Color.FromHex("#F39C12"),
                GameGUI.Core.Color.FromHex("#9B59B6"),
                GameGUI.Core.Color.FromHex("#1ABC9C"),
                GameGUI.Core.Color.FromHex("#E67E22")
            };

            for (int i = 0; i < 6; i++)
            {
                var demoElement = new Panel
                {
                    Name = $"DemoElement{i}",
                    Position = new GameGUI.Core.Vector2(10 + (i % 3) * 85, 90 + (i / 3) * 85),
                    Size = new GameGUI.Core.Vector2(75, 75),
                    Style = new GUIStyle
                    {
                        BackgroundColor = colors[i],
                        BorderColor = GameGUI.Core.Color.White,
                        BorderWidth = 1,
                        BorderRadius = 5
                    }
                };

                var label = new Label($"{i + 1}")
                {
                    Position = GameGUI.Core.Vector2.Zero,
                    Size = new GameGUI.Core.Vector2(75, 75),
                    Style = new GUIStyle
                    {
                        TextColor = GameGUI.Core.Color.White,
                        FontSize = 20,
                        FontStyle = FontStyle.Bold,
                        TextAlignment = TextAlignment.Center,
                        TextVerticalAlignment = TextVerticalAlignment.Middle,
                        BackgroundColor = GameGUI.Core.Color.Transparent
                    }
                };

                demoElement.AddChild(label);
                _guiBridge.AddElement(demoElement, _layoutDemoPanel);
            }
        }

        private void CreateDynamicContentDemo()
        {
            var dynamicPanel = UnityGUIHelper.CreatePanel(
                new Vector2(750, 50),
                new Vector2(300, 300),
                new Color(0.2f, 0.1f, 0.15f, 0.95f)
            );
            dynamicPanel.Style.BorderColor = GameGUI.Core.Color.FromHex("#E91E63");
            dynamicPanel.Style.BorderWidth = 2;
            dynamicPanel.Style.BorderRadius = 10;
            dynamicPanel.Style.Padding = new Padding(15);
            _guiBridge.AddElement(dynamicPanel);

            // Title
            var title = UnityGUIHelper.CreateLabel(
                "Dynamic Content",
                Vector2.zero,
                new Vector2(270, 25)
            );
            title.Style.TextColor = GameGUI.Core.Color.White;
            title.Style.FontSize = 16;
            title.Style.FontStyle = FontStyle.Bold;
            title.Style.TextAlignment = TextAlignment.Center;
            _guiBridge.AddElement(title, dynamicPanel);

            // Add button
            var addButton = UnityGUIHelper.CreateButton(
                "Add Button",
                new Vector2(0, 40),
                new Vector2(100, 30),
                AddDynamicButton
            );
            addButton.Style.BackgroundColor = GameGUI.Core.Color.FromHex("#4CAF50");
            addButton.Style.HoverStyle = new GUIStyle { BackgroundColor = GameGUI.Core.Color.FromHex("#45A049") };
            _guiBridge.AddElement(addButton, dynamicPanel);

            // Remove button
            var removeButton = UnityGUIHelper.CreateButton(
                "Remove Button",
                new Vector2(110, 40),
                new Vector2(100, 30),
                RemoveDynamicButton
            );
            removeButton.Style.BackgroundColor = GameGUI.Core.Color.FromHex("#F44336");
            removeButton.Style.HoverStyle = new GUIStyle { BackgroundColor = GameGUI.Core.Color.FromHex("#DA190B") };
            _guiBridge.AddElement(removeButton, dynamicPanel);

            // Clear all button
            var clearButton = UnityGUIHelper.CreateButton(
                "Clear All",
                new Vector2(220, 40),
                new Vector2(80, 30),
                ClearDynamicButtons
            );
            clearButton.Style.BackgroundColor = GameGUI.Core.Color.FromHex("#FF9800");
            clearButton.Style.HoverStyle = new GUIStyle { BackgroundColor = GameGUI.Core.Color.FromHex("#F57C00") };
            _guiBridge.AddElement(clearButton, dynamicPanel);

            // Dynamic content area
            var contentArea = UnityGUIHelper.CreatePanel(
                new Vector2(0, 80),
                new Vector2(270, 180),
                new Color(0.1f, 0.1f, 0.1f, 0.5f)
            );
            contentArea.Style.BorderColor = GameGUI.Core.Color.FromHex("#555555");
            contentArea.Style.BorderWidth = 1;
            contentArea.Name = "DynamicContentArea";
            _guiBridge.AddElement(contentArea, dynamicPanel);
        }

        private void OnSliderValueChanged(Slider slider, float value)
        {
            if (_progressBar != null)
            {
                _progressBar.Value = value / 100f;

                var progressLabel = _guiBridge.FindElement<Label>("ProgressLabel");
                if (progressLabel != null)
                {
                    progressLabel.Text = $"Progress: {value:F0}%";
                }
            }
        }

        private void ApplyStackLayout()
        {
            var stackLayout = new StackLayout(StackDirection.Vertical, spacing: 10)
            {
                Alignment = StackAlignment.Center
            };

            var demoElements = GetLayoutDemoElements();
            if (demoElements.Count > 0)
            {
                // Create a container for the layout
                var layoutContainer = new GameGUI.Core.Rect(0, 80, 270, 280);

                // Position elements manually for demo
                float currentY = 80;
                foreach (var element in demoElements)
                {
                    element.Position = new GameGUI.Core.Vector2(97.5f, currentY); // Center horizontally
                    currentY += 85;
                }
            }
        }

        private void ApplyGridLayout()
        {
            var demoElements = GetLayoutDemoElements();
            if (demoElements.Count > 0)
            {
                // Reset to grid positions
                for (int i = 0; i < demoElements.Count; i++)
                {
                    var element = demoElements[i];
                    element.Position = new GameGUI.Core.Vector2(10 + (i % 3) * 85, 90 + (i / 3) * 85);
                }
            }
        }

        private List<IGUIElement> GetLayoutDemoElements()
        {
            var elements = new List<IGUIElement>();
            for (int i = 0; i < 6; i++)
            {
                var element = _guiBridge.FindElement($"DemoElement{i}");
                if (element != null)
                    elements.Add(element);
            }
            return elements;
        }

        private void AddDynamicButton()
        {
            var contentArea = _guiBridge.FindElement("DynamicContentArea");
            if (contentArea != null)
            {
                var buttonIndex = _dynamicButtons.Count;
                var button = UnityGUIHelper.CreateButton(
                    $"Dynamic {buttonIndex + 1}",
                    new Vector2(10 + (buttonIndex % 4) * 65, 10 + (buttonIndex / 4) * 35),
                    new Vector2(60, 30),
                    () => Debug.Log($"Dynamic button {buttonIndex + 1} clicked!")
                );

                button.Style.BackgroundColor = GameGUI.Core.Color.FromHex("#9C27B0");
                button.Style.HoverStyle = new GUIStyle { BackgroundColor = GameGUI.Core.Color.FromHex("#7B1FA2") };
                button.Style.FontSize = 10;

                _guiBridge.AddElement(button, contentArea);
                _dynamicButtons.Add(button);
            }
        }

        private void RemoveDynamicButton()
        {
            if (_dynamicButtons.Count > 0)
            {
                var lastButton = _dynamicButtons[_dynamicButtons.Count - 1];
                _guiBridge.RemoveElement(lastButton);
                _dynamicButtons.RemoveAt(_dynamicButtons.Count - 1);
            }
        }

        private void ClearDynamicButtons()
        {
            foreach (var button in _dynamicButtons)
            {
                _guiBridge.RemoveElement(button);
            }
            _dynamicButtons.Clear();
        }

        // Unity Editor helpers
        #if UNITY_EDITOR
        [UnityEditor.MenuItem("GameGUI/Create Advanced Example")]
        private static void CreateAdvancedExample()
        {
            var go = new GameObject("GameGUI Advanced Example");
            go.AddComponent<AdvancedGUIExample>();
            UnityEditor.Selection.activeGameObject = go;
        }
        #endif
    }

    // Custom elements for the advanced example
    public class ProgressBar : GUIElement
    {
        private float _value = 0f;

        public float Value
        {
            get => _value;
            set => _value = Mathf.Clamp01(value);
        }

        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();

            // Draw background
            renderer.DrawRect(bounds, Style.BackgroundColor);

            // Draw progress fill
            if (Value > 0)
            {
                var fillWidth = bounds.Width * Value;
                var fillRect = new GameGUI.Core.Rect(bounds.X, bounds.Y, fillWidth, bounds.Height);
                renderer.DrawRect(fillRect, Style.BorderColor);
            }

            // Draw border
            if (Style.BorderWidth > 0)
            {
                renderer.DrawRectOutline(bounds, Style.BorderColor, Style.BorderWidth);
            }
        }
    }

    public class Slider : GUIElement
    {
        private float _value = 0f;
        private float _minValue = 0f;
        private float _maxValue = 1f;
        private bool _isDragging = false;

        public float Value
        {
            get => _value;
            set
            {
                var newValue = Mathf.Clamp(value, _minValue, _maxValue);
                if (Mathf.Abs(_value - newValue) > float.Epsilon)
                {
                    _value = newValue;
                    OnValueChanged?.Invoke(this, _value);
                }
            }
        }

        public float MinValue
        {
            get => _minValue;
            set
            {
                _minValue = value;
                if (_value < _minValue) Value = _minValue;
            }
        }

        public float MaxValue
        {
            get => _maxValue;
            set
            {
                _maxValue = value;
                if (_value > _maxValue) Value = _maxValue;
            }
        }

        public System.Action<Slider, float> OnValueChanged;

        public Slider()
        {
            Interactive = true;
            Style.BackgroundColor = GameGUI.Core.Color.FromHex("#E0E0E0");
            Style.BorderColor = GameGUI.Core.Color.FromHex("#2196F3");
        }

        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();

            // Draw track
            renderer.DrawRect(bounds, Style.BackgroundColor);

            // Draw thumb
            var thumbWidth = 10f;
            var trackWidth = bounds.Width - thumbWidth;
            var thumbPosition = (Value - MinValue) / (MaxValue - MinValue) * trackWidth;
            var thumbRect = new GameGUI.Core.Rect(bounds.X + thumbPosition, bounds.Y, thumbWidth, bounds.Height);

            renderer.DrawRect(thumbRect, Style.BorderColor);
        }
    }
}