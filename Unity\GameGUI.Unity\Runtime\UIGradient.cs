using UnityEngine;
using UnityEngine.UI;

namespace GameGUI.Unity
{
    /// <summary>
    /// Gradient effect for Unity UI elements
    /// Applies a four-corner gradient to any UI Graphic component
    /// </summary>
    [AddComponentMenu("UI/Effects/Gradient")]
    [RequireComponent(typeof(Graphic))]
    public class UIGradient : BaseMeshEffect
    {
        [SerializeField] public Color topLeft = Color.white;
        [SerializeField] public Color topRight = Color.white;
        [SerializeField] public Color bottomLeft = Color.white;
        [SerializeField] public Color bottomRight = Color.white;
        
        [SerializeField] private GradientType gradientType = GradientType.FourCorner;
        
        public enum GradientType
        {
            Vertical,
            Horizontal,
            FourCorner
        }
        
        public override void ModifyMesh(VertexHelper vh)
        {
            if (!IsActive())
                return;
            
            var vertexList = new System.Collections.Generic.List<UIVertex>();
            vh.GetUIVertexStream(vertexList);
            
            ModifyVertices(vertexList);
            
            vh.Clear();
            vh.AddUIVertexTriangleStream(vertexList);
        }
        
        private void ModifyVertices(System.Collections.Generic.List<UIVertex> vertexList)
        {
            if (vertexList.Count == 0)
                return;
            
            // Get bounds
            float minX = float.MaxValue;
            float minY = float.MaxValue;
            float maxX = float.MinValue;
            float maxY = float.MinValue;
            
            for (int i = 0; i < vertexList.Count; i++)
            {
                var vertex = vertexList[i];
                minX = Mathf.Min(minX, vertex.position.x);
                minY = Mathf.Min(minY, vertex.position.y);
                maxX = Mathf.Max(maxX, vertex.position.x);
                maxY = Mathf.Max(maxY, vertex.position.y);
            }
            
            float width = maxX - minX;
            float height = maxY - minY;
            
            // Apply gradient to each vertex
            for (int i = 0; i < vertexList.Count; i++)
            {
                var vertex = vertexList[i];
                
                float normalizedX = (vertex.position.x - minX) / width;
                float normalizedY = (vertex.position.y - minY) / height;
                
                Color color = Color.white;
                
                switch (gradientType)
                {
                    case GradientType.Vertical:
                        color = Color.Lerp(bottomLeft, topLeft, normalizedY);
                        break;
                    
                    case GradientType.Horizontal:
                        color = Color.Lerp(topLeft, topRight, normalizedX);
                        break;
                    
                    case GradientType.FourCorner:
                        Color top = Color.Lerp(topLeft, topRight, normalizedX);
                        Color bottom = Color.Lerp(bottomLeft, bottomRight, normalizedX);
                        color = Color.Lerp(bottom, top, normalizedY);
                        break;
                }
                
                vertex.color = color * vertex.color;
                vertexList[i] = vertex;
            }
        }
        
        public void SetVerticalGradient(Color top, Color bottom)
        {
            gradientType = GradientType.Vertical;
            topLeft = top;
            topRight = top;
            bottomLeft = bottom;
            bottomRight = bottom;
            graphic.SetVerticesDirty();
        }
        
        public void SetHorizontalGradient(Color left, Color right)
        {
            gradientType = GradientType.Horizontal;
            topLeft = left;
            topRight = right;
            bottomLeft = left;
            bottomRight = right;
            graphic.SetVerticesDirty();
        }
        
        public void SetFourCornerGradient(Color tl, Color tr, Color bl, Color br)
        {
            gradientType = GradientType.FourCorner;
            topLeft = tl;
            topRight = tr;
            bottomLeft = bl;
            bottomRight = br;
            graphic.SetVerticesDirty();
        }
    }
}

