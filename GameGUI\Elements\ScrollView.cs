using GameGUI.Core;
using System;

namespace GameGUI.Elements
{
    /// <summary>
    /// ScrollView element for scrollable content
    /// Super easy to use with automatic scrollbar management
    /// </summary>
    public class ScrollView : GUIElement
    {
        private Panel _viewport;
        private Panel _content;
        private Slider _verticalScrollbar;
        private Slider _horizontalScrollbar;
        private Vector2 _scrollPosition;
        private Vector2 _contentSize;
        private bool _showVerticalScrollbar;
        private bool _showHorizontalScrollbar;
        private float _scrollbarWidth;
        private ScrollbarVisibility _verticalScrollbarVisibility;
        private ScrollbarVisibility _horizontalScrollbarVisibility;
        
        public Panel Content => _content;
        
        public Vector2 ScrollPosition
        {
            get => _scrollPosition;
            set
            {
                var newPos = new Vector2(
                    Mathf.Clamp(value.X, 0, GetMaxScrollX()),
                    Mathf.Clamp(value.Y, 0, GetMaxScrollY())
                );
                
                if (_scrollPosition != newPos)
                {
                    _scrollPosition = newPos;
                    UpdateContentPosition();
                    OnScrollChanged?.Invoke(this, _scrollPosition);
                }
            }
        }
        
        public Vector2 ContentSize
        {
            get => _contentSize;
            set
            {
                _contentSize = value;
                if (_content != null)
                    _content.Size = value;
                UpdateScrollbars();
            }
        }
        
        public float ScrollbarWidth
        {
            get => _scrollbarWidth;
            set
            {
                _scrollbarWidth = value;
                UpdateScrollbars();
            }
        }
        
        public ScrollbarVisibility VerticalScrollbarVisibility
        {
            get => _verticalScrollbarVisibility;
            set
            {
                _verticalScrollbarVisibility = value;
                UpdateScrollbars();
            }
        }
        
        public ScrollbarVisibility HorizontalScrollbarVisibility
        {
            get => _horizontalScrollbarVisibility;
            set
            {
                _horizontalScrollbarVisibility = value;
                UpdateScrollbars();
            }
        }
        
        public float ScrollSensitivity { get; set; } = 20f;
        
        public event Action<ScrollView, Vector2> OnScrollChanged;
        
        public ScrollView()
        {
            Size = new Vector2(400, 300);
            _scrollbarWidth = 12f;
            _verticalScrollbarVisibility = ScrollbarVisibility.Auto;
            _horizontalScrollbarVisibility = ScrollbarVisibility.Auto;
            _contentSize = new Vector2(400, 600);
            
            InitializeComponents();
        }
        
        private void InitializeComponents()
        {
            // Create viewport (clipping area)
            _viewport = new Panel
            {
                Position = Vector2.Zero,
                Size = Size,
                Style = new GUIStyle
                {
                    BackgroundColor = Color.FromHex("#FFFFFF"),
                    BorderColor = Color.FromHex("#CCCCCC"),
                    BorderWidth = 1
                }
            };
            AddChild(_viewport);
            
            // Create content container
            _content = new Panel
            {
                Position = Vector2.Zero,
                Size = _contentSize,
                Style = new GUIStyle
                {
                    BackgroundColor = new GameGUI.Core.Color(0, 0, 0, 0) // Transparent
                }
            };
            _viewport.AddChild(_content);
            
            // Create vertical scrollbar
            _verticalScrollbar = new Slider(0, 100, 0)
            {
                Direction = SliderDirection.Vertical,
                Visible = false
            };
            _verticalScrollbar.OnValueChanged += (slider, value) =>
            {
                float maxScroll = GetMaxScrollY();
                ScrollPosition = new Vector2(_scrollPosition.X, (value / 100f) * maxScroll);
            };
            AddChild(_verticalScrollbar);
            
            // Create horizontal scrollbar
            _horizontalScrollbar = new Slider(0, 100, 0)
            {
                Direction = SliderDirection.Horizontal,
                Visible = false
            };
            _horizontalScrollbar.OnValueChanged += (slider, value) =>
            {
                float maxScroll = GetMaxScrollX();
                ScrollPosition = new Vector2((value / 100f) * maxScroll, _scrollPosition.Y);
            };
            AddChild(_horizontalScrollbar);
            
            UpdateScrollbars();
        }
        
        private void UpdateScrollbars()
        {
            if (_viewport == null || _content == null)
                return;
            
            float viewportWidth = Size.X;
            float viewportHeight = Size.Y;
            
            // Determine if scrollbars are needed
            bool needsVertical = _contentSize.Y > viewportHeight;
            bool needsHorizontal = _contentSize.X > viewportWidth;
            
            // Apply visibility settings
            _showVerticalScrollbar = _verticalScrollbarVisibility == ScrollbarVisibility.Always ||
                (_verticalScrollbarVisibility == ScrollbarVisibility.Auto && needsVertical);
            
            _showHorizontalScrollbar = _horizontalScrollbarVisibility == ScrollbarVisibility.Always ||
                (_horizontalScrollbarVisibility == ScrollbarVisibility.Auto && needsHorizontal);
            
            // Adjust viewport size based on scrollbars
            float adjustedWidth = viewportWidth - (_showVerticalScrollbar ? _scrollbarWidth : 0);
            float adjustedHeight = viewportHeight - (_showHorizontalScrollbar ? _scrollbarWidth : 0);
            
            _viewport.Size = new Vector2(adjustedWidth, adjustedHeight);
            
            // Position and size vertical scrollbar
            if (_showVerticalScrollbar)
            {
                _verticalScrollbar.Position = new Vector2(adjustedWidth, 0);
                _verticalScrollbar.Size = new Vector2(_scrollbarWidth, adjustedHeight);
                _verticalScrollbar.Visible = true;
            }
            else
            {
                _verticalScrollbar.Visible = false;
            }
            
            // Position and size horizontal scrollbar
            if (_showHorizontalScrollbar)
            {
                _horizontalScrollbar.Position = new Vector2(0, adjustedHeight);
                _horizontalScrollbar.Size = new Vector2(adjustedWidth, _scrollbarWidth);
                _horizontalScrollbar.Visible = true;
            }
            else
            {
                _horizontalScrollbar.Visible = false;
            }
        }
        
        private void UpdateContentPosition()
        {
            if (_content != null)
            {
                _content.Position = new Vector2(-_scrollPosition.X, -_scrollPosition.Y);
            }
        }
        
        private float GetMaxScrollX()
        {
            return Math.Max(0, _contentSize.X - _viewport.Size.X);
        }
        
        private float GetMaxScrollY()
        {
            return Math.Max(0, _contentSize.Y - _viewport.Size.Y);
        }
        
        public void ScrollTo(Vector2 position)
        {
            ScrollPosition = position;
        }
        
        public void ScrollToTop()
        {
            ScrollPosition = new Vector2(_scrollPosition.X, 0);
        }
        
        public void ScrollToBottom()
        {
            ScrollPosition = new Vector2(_scrollPosition.X, GetMaxScrollY());
        }
        
        public void ScrollToLeft()
        {
            ScrollPosition = new Vector2(0, _scrollPosition.Y);
        }
        
        public void ScrollToRight()
        {
            ScrollPosition = new Vector2(GetMaxScrollX(), _scrollPosition.Y);
        }
        
        public void ScrollVertical(float delta)
        {
            ScrollPosition = new Vector2(_scrollPosition.X, _scrollPosition.Y + delta);
        }
        
        public void ScrollHorizontal(float delta)
        {
            ScrollPosition = new Vector2(_scrollPosition.X + delta, _scrollPosition.Y);
        }
        
        public void PageUp()
        {
            ScrollVertical(-_viewport.Size.Y);
        }
        
        public void PageDown()
        {
            ScrollVertical(_viewport.Size.Y);
        }
        
        public void AddContent(IGUIElement element)
        {
            _content.AddChild(element);
            AutoResizeContent();
        }
        
        public void RemoveContent(IGUIElement element)
        {
            _content.RemoveChild(element);
            AutoResizeContent();
        }
        
        public void ClearContent()
        {
            _content.Children.Clear();
        }
        
        public void AutoResizeContent()
        {
            // Calculate content size based on children
            float maxX = 0;
            float maxY = 0;
            
            foreach (var child in _content.Children)
            {
                var bounds = child.GetBounds();
                maxX = Math.Max(maxX, bounds.X + bounds.Width);
                maxY = Math.Max(maxY, bounds.Y + bounds.Height);
            }
            
            ContentSize = new Vector2(maxX, maxY);
        }
        
        public float GetVerticalScrollPercentage()
        {
            float maxScroll = GetMaxScrollY();
            return maxScroll > 0 ? _scrollPosition.Y / maxScroll : 0;
        }
        
        public float GetHorizontalScrollPercentage()
        {
            float maxScroll = GetMaxScrollX();
            return maxScroll > 0 ? _scrollPosition.X / maxScroll : 0;
        }
        
        public bool IsScrolledToBottom()
        {
            return Mathf.Approximately(_scrollPosition.Y, GetMaxScrollY());
        }
        
        public bool IsScrolledToTop()
        {
            return Mathf.Approximately(_scrollPosition.Y, 0);
        }
    }
    
    public enum ScrollbarVisibility
    {
        Auto,
        Always,
        Never
    }
}

