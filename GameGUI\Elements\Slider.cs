using GameGUI.Core;
using System;

namespace GameGUI.Elements
{
    /// <summary>
    /// Slider element for selecting a value within a range
    /// Super easy to use with automatic layout and styling
    /// </summary>
    public class Slider : GUIElement
    {
        private float _value;
        private float _minValue;
        private float _maxValue;
        private bool _wholeNumbers;
        private SliderDirection _direction;
        private bool _isDragging;
        
        public float Value
        {
            get => _value;
            set
            {
                var newValue = Mathf.Clamp(value, _minValue, _maxValue);
                if (_wholeNumbers)
                    newValue = Mathf.Round(newValue);
                
                if (!Mathf.Approximately(_value, newValue))
                {
                    _value = newValue;
                    OnValueChanged?.Invoke(this, _value);
                }
            }
        }
        
        public float MinValue
        {
            get => _minValue;
            set
            {
                _minValue = value;
                Value = _value; // Re-clamp current value
            }
        }
        
        public float MaxValue
        {
            get => _maxValue;
            set
            {
                _maxValue = value;
                Value = _value; // Re-clamp current value
            }
        }
        
        public bool WholeNumbers
        {
            get => _wholeNumbers;
            set
            {
                _wholeNumbers = value;
                if (value)
                    Value = Mathf.Round(_value);
            }
        }
        
        public SliderDirection Direction
        {
            get => _direction;
            set => _direction = value;
        }
        
        public GUIStyle HandleStyle { get; set; }
        public GUIStyle FillStyle { get; set; }
        public GUIStyle BackgroundStyle { get; set; }
        
        public float HandleSize { get; set; } = 20f;
        
        public event Action<Slider, float> OnValueChanged;
        public event Action<Slider> OnDragStart;
        public event Action<Slider> OnDragEnd;
        
        public Slider(float minValue = 0f, float maxValue = 1f, float initialValue = 0f)
        {
            _minValue = minValue;
            _maxValue = maxValue;
            _value = Mathf.Clamp(initialValue, minValue, maxValue);
            _direction = SliderDirection.Horizontal;
            
            Size = new Vector2(200, 20);
            
            InitializeStyles();
        }
        
        private void InitializeStyles()
        {
            // Default background style
            BackgroundStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#E0E0E0"),
                BorderRadius = 4,
                BorderWidth = 1,
                BorderColor = Color.FromHex("#CCCCCC")
            };
            
            // Default fill style
            FillStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#2196F3"),
                BorderRadius = 4
            };
            
            // Default handle style
            HandleStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#FFFFFF"),
                BorderRadius = 10,
                BorderWidth = 2,
                BorderColor = Color.FromHex("#2196F3")
            };
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            
            // Draw background
            if (BackgroundStyle != null)
            {
                renderer.DrawRoundedRect(bounds, BackgroundStyle.BorderRadius, BackgroundStyle.BackgroundColor);
                if (BackgroundStyle.BorderWidth > 0)
                {
                    renderer.DrawRoundedRectOutline(bounds, BackgroundStyle.BorderRadius, 
                        BackgroundStyle.BorderColor, BackgroundStyle.BorderWidth);
                }
            }
            
            // Calculate fill rect
            float normalizedValue = (_value - _minValue) / (_maxValue - _minValue);
            Rect fillRect;
            
            if (_direction == SliderDirection.Horizontal)
            {
                fillRect = new Rect(
                    bounds.X,
                    bounds.Y,
                    bounds.Width * normalizedValue,
                    bounds.Height
                );
            }
            else
            {
                float fillHeight = bounds.Height * normalizedValue;
                fillRect = new Rect(
                    bounds.X,
                    bounds.Y + bounds.Height - fillHeight,
                    bounds.Width,
                    fillHeight
                );
            }
            
            // Draw fill
            if (FillStyle != null && normalizedValue > 0)
            {
                renderer.DrawRoundedRect(fillRect, FillStyle.BorderRadius, FillStyle.BackgroundColor);
            }
            
            // Calculate handle position
            Vector2 handlePos;
            if (_direction == SliderDirection.Horizontal)
            {
                handlePos = new Vector2(
                    bounds.X + bounds.Width * normalizedValue - HandleSize * 0.5f,
                    bounds.Y + bounds.Height * 0.5f - HandleSize * 0.5f
                );
            }
            else
            {
                handlePos = new Vector2(
                    bounds.X + bounds.Width * 0.5f - HandleSize * 0.5f,
                    bounds.Y + bounds.Height * (1f - normalizedValue) - HandleSize * 0.5f
                );
            }
            
            var handleRect = new Rect(handlePos.X, handlePos.Y, HandleSize, HandleSize);
            
            // Draw handle
            if (HandleStyle != null)
            {
                renderer.DrawRoundedRect(handleRect, HandleStyle.BorderRadius, HandleStyle.BackgroundColor);
                if (HandleStyle.BorderWidth > 0)
                {
                    renderer.DrawRoundedRectOutline(handleRect, HandleStyle.BorderRadius,
                        HandleStyle.BorderColor, HandleStyle.BorderWidth);
                }
            }
        }
        
        protected override void OnUpdate(float deltaTime)
        {
            // Handle input will be managed by event system
        }
        
        public void HandleDrag(Vector2 position)
        {
            if (!_isDragging)
            {
                _isDragging = true;
                OnDragStart?.Invoke(this);
            }
            
            var bounds = GetBounds();
            float normalizedValue;
            
            if (_direction == SliderDirection.Horizontal)
            {
                normalizedValue = (position.X - bounds.X) / bounds.Width;
            }
            else
            {
                normalizedValue = 1f - ((position.Y - bounds.Y) / bounds.Height);
            }
            
            normalizedValue = Mathf.Clamp01(normalizedValue);
            Value = _minValue + normalizedValue * (_maxValue - _minValue);
        }
        
        public void EndDrag()
        {
            if (_isDragging)
            {
                _isDragging = false;
                OnDragEnd?.Invoke(this);
            }
        }
        
        public float GetNormalizedValue()
        {
            return (_value - _minValue) / (_maxValue - _minValue);
        }
        
        public void SetNormalizedValue(float normalizedValue)
        {
            Value = _minValue + Mathf.Clamp01(normalizedValue) * (_maxValue - _minValue);
        }
    }
    
    public enum SliderDirection
    {
        Horizontal,
        Vertical
    }
    
    /// <summary>
    /// Simple math utilities for slider
    /// </summary>
    public static class Mathf
    {
        public static float Clamp(float value, float min, float max)
        {
            if (value < min) return min;
            if (value > max) return max;
            return value;
        }
        
        public static float Clamp01(float value)
        {
            return Clamp(value, 0f, 1f);
        }
        
        public static float Round(float value)
        {
            return (float)System.Math.Round(value);
        }
        
        public static bool Approximately(float a, float b)
        {
            return System.Math.Abs(a - b) < 0.0001f;
        }
    }
}

