using System;

namespace GameGUI.Core
{
    /// <summary>
    /// Interface for rendering GUI elements to different backends (Unity, DirectX, OpenGL, etc.)
    /// </summary>
    public interface IGUIRenderer
    {
        // Rendering context
        void BeginRender();
        void EndRender();
        void SetClipRect(Rect clipRect);
        void ClearClipRect();
        
        // Basic shapes
        void DrawRect(Rect rect, Color color);
        void DrawRectOutline(Rect rect, Color color, float lineWidth);
        void DrawRoundedRect(Rect rect, float radius, Color color);
        void DrawRoundedRectOutline(Rect rect, float radius, Color color, float lineWidth);
        void DrawCircle(Vector2 center, float radius, Color color);
        void DrawCircleOutline(Vector2 center, float radius, Color color, float lineWidth);
        void DrawLine(Vector2 start, Vector2 end, Color color, float lineWidth);
        
        // Text rendering
        void DrawText(string text, Vector2 position, GUIStyle style);
        Vector2 MeasureText(string text, GUIStyle style);
        
        // Image rendering
        void DrawImage(string imagePath, Rect destRect, Rect? sourceRect = null, Color? tint = null);
        void DrawImage(object imageResource, Rect destRect, Rect? sourceRect = null, Color? tint = null);
        
        // Gradient and effects
        void DrawGradient(Rect rect, Color topLeft, Color topRight, Color bottomLeft, Color bottomRight);
        void DrawShadow(Rect rect, Vector2 offset, float blur, Color color);
        
        // Transform operations
        void PushTransform(Vector2 translation, float rotation, Vector2 scale);
        void PopTransform();
        
        // Resource management
        object LoadTexture(string path);
        void UnloadTexture(object texture);
        object LoadFont(string fontFamily, float fontSize, FontStyle style);
        void UnloadFont(object font);
        
        // Utility
        Vector2 GetScreenSize();
        float GetDPIScale();
    }
    
    /// <summary>
    /// Event arguments for GUI input events
    /// </summary>
    public class GUIInputEventArgs : EventArgs
    {
        public Vector2 Position { get; set; }
        public Vector2 Delta { get; set; }
        public MouseButton Button { get; set; }
        public KeyCode Key { get; set; }
        public char Character { get; set; }
        public bool Handled { get; set; }
    }
    
    public enum MouseButton
    {
        None,
        Left,
        Right,
        Middle,
        X1,
        X2
    }
    
    public enum KeyCode
    {
        None,
        A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z,
        Alpha0, Alpha1, Alpha2, Alpha3, Alpha4, Alpha5, Alpha6, Alpha7, Alpha8, Alpha9,
        Space, Enter, Escape, Backspace, Delete, Tab, LeftShift, RightShift,
        LeftControl, RightControl, LeftAlt, RightAlt,
        UpArrow, DownArrow, LeftArrow, RightArrow,
        F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12
    }
}
