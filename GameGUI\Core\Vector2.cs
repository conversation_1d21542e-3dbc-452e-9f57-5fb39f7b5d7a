using System;

namespace GameGUI.Core
{
    /// <summary>
    /// 2D Vector structure for positions, sizes, and other 2D data
    /// </summary>
    [Serializable]
    public struct Vector2 : IEquatable<Vector2>
    {
        public float X;
        public float Y;
        
        public static readonly Vector2 Zero = new Vector2(0, 0);
        public static readonly Vector2 One = new Vector2(1, 1);
        public static readonly Vector2 Up = new Vector2(0, 1);
        public static readonly Vector2 Down = new Vector2(0, -1);
        public static readonly Vector2 Left = new Vector2(-1, 0);
        public static readonly Vector2 Right = new Vector2(1, 0);
        
        public Vector2(float x, float y)
        {
            X = x;
            Y = y;
        }
        
        public Vector2(float value)
        {
            X = value;
            Y = value;
        }
        
        public float Magnitude => (float)Math.Sqrt(X * X + Y * Y);
        public float SqrMagnitude => X * X + Y * Y;
        public Vector2 Normalized => Magnitude > 0 ? this / Magnitude : Zero;
        
        public static Vector2 operator +(Vector2 a, Vector2 b) => new Vector2(a.X + b.X, a.Y + b.Y);
        public static Vector2 operator -(Vector2 a, Vector2 b) => new Vector2(a.X - b.X, a.Y - b.Y);
        public static Vector2 operator *(Vector2 a, float scalar) => new Vector2(a.X * scalar, a.Y * scalar);
        public static Vector2 operator *(float scalar, Vector2 a) => new Vector2(a.X * scalar, a.Y * scalar);
        public static Vector2 operator /(Vector2 a, float scalar) => new Vector2(a.X / scalar, a.Y / scalar);
        public static Vector2 operator -(Vector2 a) => new Vector2(-a.X, -a.Y);
        
        public static bool operator ==(Vector2 a, Vector2 b) => Math.Abs(a.X - b.X) < float.Epsilon && Math.Abs(a.Y - b.Y) < float.Epsilon;
        public static bool operator !=(Vector2 a, Vector2 b) => !(a == b);
        
        public bool Equals(Vector2 other) => this == other;
        public override bool Equals(object obj) => obj is Vector2 other && Equals(other);
        public override int GetHashCode() => HashCode.Combine(X, Y);
        public override string ToString() => $"({X:F2}, {Y:F2})";
        
        public static float Distance(Vector2 a, Vector2 b) => (a - b).Magnitude;
        public static float Dot(Vector2 a, Vector2 b) => a.X * b.X + a.Y * b.Y;
        public static Vector2 Lerp(Vector2 a, Vector2 b, float t) => a + (b - a) * Math.Max(0, Math.Min(1, t));
    }
}
