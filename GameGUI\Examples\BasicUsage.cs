using GameGUI.Core;
using GameGUI.Elements;
using GameGUI.Layout;
using GameGUI.Theming;
using System;

namespace GameGUI.Examples
{
    /// <summary>
    /// Basic usage examples for the GameGUI system
    /// </summary>
    public static class BasicUsage
    {
        /// <summary>
        /// Example 1: Creating basic elements
        /// </summary>
        public static void CreateBasicElements()
        {
            // Create a panel container
            var panel = new Panel
            {
                Name = "MainPanel",
                Position = new Vector2(100, 100),
                Size = new Vector2(300, 200),
                Style = new GUIStyle
                {
                    BackgroundColor = Color.FromHex("#F0F0F0"),
                    BorderColor = Color.FromHex("#CCCCCC"),
                    BorderWidth = 2,
                    BorderRadius = 8,
                    Padding = new Padding(10)
                }
            };
            
            // Create a title label
            var titleLabel = new Label("Welcome to GameGUI!")
            {
                Position = new Vector2(0, 0),
                Size = new Vector2(280, 30),
                Style = new GUIStyle
                {
                    TextColor = Color.FromHex("#333333"),
                    FontSize = 16,
                    FontStyle = FontStyle.Bold,
                    TextAlignment = TextAlignment.Center
                }
            };
            
            // Create a button
            var button = new Button("Click Me!")
            {
                Position = new Vector2(0, 40),
                Size = new Vector2(100, 30)
            };
            
            // Handle button click
            button.OnButtonClick += (btn) =>
            {
                Console.WriteLine($"Button '{btn.Text}' was clicked!");
            };
            
            // Create a text input
            var textBox = new TextBox("Enter your name...")
            {
                Position = new Vector2(0, 80),
                Size = new Vector2(200, 25)
            };
            
            // Handle text changes
            textBox.TextChanged += (tb, newText) =>
            {
                Console.WriteLine($"Text changed to: {newText}");
            };
            
            // Add elements to panel
            panel.AddChild(titleLabel);
            panel.AddChild(button);
            panel.AddChild(textBox);
        }
        
        /// <summary>
        /// Example 2: Using layout managers
        /// </summary>
        public static void UseLayoutManagers()
        {
            // Create container panel
            var container = new Panel
            {
                Position = new Vector2(50, 50),
                Size = new Vector2(400, 300),
                Style = new GUIStyle
                {
                    BackgroundColor = Color.White,
                    BorderColor = Color.Gray,
                    BorderWidth = 1,
                    Padding = new Padding(20)
                }
            };
            
            // Create buttons for vertical stack
            var button1 = new Button("Button 1") { Size = new Vector2(120, 30) };
            var button2 = new Button("Button 2") { Size = new Vector2(120, 30) };
            var button3 = new Button("Button 3") { Size = new Vector2(120, 30) };
            
            container.AddChild(button1);
            container.AddChild(button2);
            container.AddChild(button3);
            
            // Apply vertical stack layout
            var stackLayout = new StackLayout(StackDirection.Vertical, spacing: 10)
            {
                Alignment = StackAlignment.Center
            };
            
            stackLayout.CalculateLayout(container, container.Children);
            
            // Example with grid layout
            var gridContainer = new Panel
            {
                Position = new Vector2(500, 50),
                Size = new Vector2(300, 200),
                Style = new GUIStyle { Padding = new Padding(10) }
            };
            
            // Create grid of buttons
            for (int i = 0; i < 6; i++)
            {
                var gridButton = new Button($"Btn {i + 1}")
                {
                    Size = new Vector2(80, 30)
                };
                gridContainer.AddChild(gridButton);
            }
            
            // Apply grid layout (2 columns)
            var gridLayout = new GridLayout(columns: 2)
            {
                Spacing = new Vector2(10, 10),
                FillCells = true
            };
            
            gridLayout.CalculateLayout(gridContainer, gridContainer.Children);
        }
        
        /// <summary>
        /// Example 3: Applying themes
        /// </summary>
        public static void ApplyThemes()
        {
            // Create elements
            var button = new Button("Themed Button");
            var textBox = new TextBox("Themed Input");
            var label = new Label("Themed Label");
            
            // Apply dark theme
            var darkTheme = GUITheme.CreateDarkTheme();
            darkTheme.ApplyToElement(button);
            darkTheme.ApplyToElement(textBox);
            darkTheme.ApplyToElement(label);
            
            // Create custom theme
            var customTheme = new GUITheme("Gaming")
            {
                Colors = new ThemeColors
                {
                    Primary = Color.FromHex("#00FF41"),      // Matrix green
                    Background = Color.FromHex("#0D1117"),   // Dark background
                    Surface = Color.FromHex("#161B22"),      // Slightly lighter
                    OnPrimary = Color.Black,
                    OnBackground = Color.FromHex("#00FF41"),
                    Border = Color.FromHex("#30363D")
                }
            };
            
            // Apply custom theme
            var customButton = new Button("Gaming Style");
            customTheme.ApplyToElement(customButton);
        }
        
        /// <summary>
        /// Example 4: Custom element creation
        /// </summary>
        public static void CreateCustomElements()
        {
            // Create a custom progress bar
            var progressBar = new ProgressBar
            {
                Position = new Vector2(100, 100),
                Size = new Vector2(200, 20),
                Value = 0.75f, // 75%
                Style = new GUIStyle
                {
                    BackgroundColor = Color.FromHex("#E0E0E0"),
                    BorderColor = Color.FromHex("#4CAF50"),
                    BorderWidth = 1
                }
            };
            
            // Create a custom slider
            var slider = new Slider
            {
                Position = new Vector2(100, 140),
                Size = new Vector2(200, 20),
                MinValue = 0,
                MaxValue = 100,
                Value = 50
            };
            
            slider.ValueChanged += (s, value) =>
            {
                Console.WriteLine($"Slider value: {value}");
                progressBar.Value = value / 100f; // Update progress bar
            };
        }
        
        /// <summary>
        /// Example 5: Event handling
        /// </summary>
        public static void HandleEvents()
        {
            var button = new Button("Interactive Button");
            var label = new Label("Status: Ready");
            
            // Multiple event handlers
            button.OnClick += (element) =>
            {
                label.Text = "Status: Button Clicked!";
            };
            
            button.OnHover += (element) =>
            {
                label.Text = "Status: Button Hovered";
            };
            
            button.OnFocus += (element) =>
            {
                label.Text = "Status: Button Focused";
            };
            
            button.OnBlur += (element) =>
            {
                label.Text = "Status: Button Lost Focus";
            };
            
            // Text input events
            var textBox = new TextBox();
            textBox.TextChanged += (tb, newText) =>
            {
                if (newText.Length > 10)
                {
                    label.Text = "Status: Text too long!";
                    label.Style.TextColor = Color.Red;
                }
                else
                {
                    label.Text = $"Status: {newText.Length} characters";
                    label.Style.TextColor = Color.Black;
                }
            };
        }
    }
    
    /// <summary>
    /// Custom progress bar element example
    /// </summary>
    public class ProgressBar : GUIElement
    {
        private float _value = 0f;
        
        public float Value
        {
            get => _value;
            set
            {
                var newValue = Math.Max(0, Math.Min(1, value));
                if (Math.Abs(_value - newValue) > float.Epsilon)
                {
                    _value = newValue;
                    RaiseValueChanged();
                }
            }
        }

        public event Action<ProgressBar, float>? ValueChanged;

        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();

            // Draw background
            renderer.DrawRect(bounds, Style.BackgroundColor);

            // Draw progress fill
            if (Value > 0)
            {
                var fillWidth = bounds.Width * Value;
                var fillRect = new Rect(bounds.X, bounds.Y, fillWidth, bounds.Height);
                renderer.DrawRect(fillRect, Style.BorderColor);
            }

            // Draw border
            if (Style.BorderWidth > 0)
            {
                renderer.DrawRectOutline(bounds, Style.BorderColor, Style.BorderWidth);
            }
        }

        protected virtual void RaiseValueChanged()
        {
            ValueChanged?.Invoke(this, Value);
        }
    }
    
    /// <summary>
    /// Custom slider element example
    /// </summary>
    public class Slider : GUIElement
    {
        private float _value = 0f;
        private float _minValue = 0f;
        private float _maxValue = 1f;

        public float Value
        {
            get => _value;
            set
            {
                var newValue = Math.Max(_minValue, Math.Min(_maxValue, value));
                if (Math.Abs(_value - newValue) > float.Epsilon)
                {
                    _value = newValue;
                    RaiseValueChanged();
                }
            }
        }

        public float MinValue
        {
            get => _minValue;
            set
            {
                _minValue = value;
                if (_value < _minValue) Value = _minValue;
            }
        }

        public float MaxValue
        {
            get => _maxValue;
            set
            {
                _maxValue = value;
                if (_value > _maxValue) Value = _maxValue;
            }
        }

        public event Action<Slider, float>? ValueChanged;

        public Slider()
        {
            Interactive = true;
            Style.BackgroundColor = Color.FromHex("#E0E0E0");
            Style.BorderColor = Color.FromHex("#2196F3");
        }

        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();

            // Draw track
            renderer.DrawRect(bounds, Style.BackgroundColor);

            // Draw thumb
            var thumbWidth = 10f;
            var trackWidth = bounds.Width - thumbWidth;
            var thumbPosition = (Value - MinValue) / (MaxValue - MinValue) * trackWidth;
            var thumbRect = new Rect(bounds.X + thumbPosition, bounds.Y, thumbWidth, bounds.Height);

            renderer.DrawRect(thumbRect, Style.BorderColor);
        }

        protected virtual void RaiseValueChanged()
        {
            ValueChanged?.Invoke(this, Value);
        }
    }
}
