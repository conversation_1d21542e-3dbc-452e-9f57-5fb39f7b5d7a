using GameGUI.Core;
using System;

namespace GameGUI.Elements
{
    /// <summary>
    /// Interactive button element
    /// </summary>
    public class Button : GUIElement
    {
        private string _text = "Button";
        private ElementState _currentState = ElementState.Normal;
        private bool _isPressed = false;
        private bool _isHovered = false;
        
        public string Text
        {
            get => _text;
            set
            {
                if (_text != value)
                {
                    _text = value ?? string.Empty;
                    OnTextChanged();
                }
            }
        }
        
        public ElementState CurrentState
        {
            get => _currentState;
            private set
            {
                if (_currentState != value)
                {
                    _currentState = value;
                    OnStateChanged();
                }
            }
        }
        
        public event Action<Button> OnButtonClick;
        
        public Button()
        {
            InitializeDefaultStyle();
            Interactive = true;
        }
        
        public Button(string text) : this()
        {
            Text = text;
        }
        
        private void InitializeDefaultStyle()
        {
            // Normal state
            Style.BackgroundColor = Color.FromHex("#E0E0E0");
            Style.BorderColor = Color.FromHex("#808080");
            Style.BorderWidth = 1;
            Style.BorderRadius = 4;
            Style.TextColor = Color.Black;
            Style.FontSize = 12;
            Style.TextAlignment = TextAlignment.Center;
            Style.TextVerticalAlignment = TextVerticalAlignment.Middle;
            Style.Padding = new Padding(8, 4, 8, 4);
            
            // Hover state
            Style.HoverStyle = new GUIStyle(Style)
            {
                BackgroundColor = Color.FromHex("#F0F0F0"),
                BorderColor = Color.FromHex("#606060")
            };
            
            // Pressed state
            Style.PressedStyle = new GUIStyle(Style)
            {
                BackgroundColor = Color.FromHex("#D0D0D0"),
                BorderColor = Color.FromHex("#404040")
            };
            
            // Disabled state
            Style.DisabledStyle = new GUIStyle(Style)
            {
                BackgroundColor = Color.FromHex("#F5F5F5"),
                BorderColor = Color.FromHex("#C0C0C0"),
                TextColor = Color.FromHex("#808080")
            };
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            var currentStyle = GetCurrentStyle();
            
            // Draw background
            if (currentStyle.BackgroundColor.A > 0)
            {
                if (currentStyle.BorderRadius > 0)
                {
                    renderer.DrawRoundedRect(bounds, currentStyle.BorderRadius, currentStyle.BackgroundColor);
                }
                else
                {
                    renderer.DrawRect(bounds, currentStyle.BackgroundColor);
                }
            }
            
            // Draw border
            if (currentStyle.BorderWidth > 0 && currentStyle.BorderColor.A > 0)
            {
                if (currentStyle.BorderRadius > 0)
                {
                    renderer.DrawRoundedRectOutline(bounds, currentStyle.BorderRadius, currentStyle.BorderColor, currentStyle.BorderWidth);
                }
                else
                {
                    renderer.DrawRectOutline(bounds, currentStyle.BorderColor, currentStyle.BorderWidth);
                }
            }
            
            // Draw text
            if (!string.IsNullOrEmpty(Text))
            {
                var textPosition = CalculateTextPosition(bounds, currentStyle);
                renderer.DrawText(Text, textPosition, currentStyle);
            }
        }
        
        private GUIStyle GetCurrentStyle()
        {
            if (!Enabled)
                return Style.DisabledStyle ?? Style;
                
            return Style.GetStateStyle(CurrentState);
        }
        
        private Vector2 CalculateTextPosition(Rect bounds, GUIStyle style)
        {
            var contentBounds = new Rect(
                bounds.X + style.Padding.Left,
                bounds.Y + style.Padding.Top,
                bounds.Width - style.Padding.Left - style.Padding.Right,
                bounds.Height - style.Padding.Top - style.Padding.Bottom
            );
            
            // For buttons, we typically center the text
            var textSize = EstimateTextSize(Text, style);
            
            float x = contentBounds.X + (contentBounds.Width - textSize.X) * 0.5f;
            float y = contentBounds.Y + (contentBounds.Height - textSize.Y) * 0.5f;
            
            return new Vector2(x, y);
        }
        
        private Vector2 EstimateTextSize(string text, GUIStyle style)
        {
            if (string.IsNullOrEmpty(text))
                return Vector2.Zero;
                
            // Rough estimation
            float charWidth = style.FontSize * 0.6f;
            float lineHeight = style.FontSize * 1.2f;
            
            return new Vector2(text.Length * charWidth, lineHeight);
        }
        
        public void HandleMouseDown(Vector2 position)
        {
            if (!Enabled || !Interactive || !ContainsPoint(position))
                return;
                
            _isPressed = true;
            UpdateState();
            TriggerFocus();
        }
        
        public void HandleMouseUp(Vector2 position)
        {
            if (!Enabled || !Interactive)
                return;
                
            bool wasPressed = _isPressed;
            _isPressed = false;
            UpdateState();
            
            if (wasPressed && ContainsPoint(position))
            {
                TriggerClick();
                OnButtonClick?.Invoke(this);
            }
        }
        
        public void HandleMouseEnter(Vector2 position)
        {
            if (!Enabled || !Interactive)
                return;
                
            _isHovered = true;
            UpdateState();
            TriggerHover();
        }
        
        public void HandleMouseLeave()
        {
            _isHovered = false;
            UpdateState();
        }
        
        private void UpdateState()
        {
            if (!Enabled)
            {
                CurrentState = ElementState.Disabled;
            }
            else if (_isPressed)
            {
                CurrentState = ElementState.Pressed;
            }
            else if (_isHovered)
            {
                CurrentState = ElementState.Hover;
            }
            else
            {
                CurrentState = ElementState.Normal;
            }
        }
        
        protected override void OnEnabledChanged()
        {
            UpdateState();
        }
        
        protected virtual void OnTextChanged() { }
        protected virtual void OnStateChanged() { }
    }
}
