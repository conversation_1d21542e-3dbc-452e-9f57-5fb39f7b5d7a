# GameGUI Designer - User Guide

## 🎨 **Opening the Designer**

1. In Unity, go to: **Window → GameGUI → GUI Designer**
2. The designer window will open with three panels:
   - **Left**: Hierarchy (element tree)
   - **Center**: Canvas (visual preview)
   - **Right**: Properties (element settings)

## 🚀 **Quick Start**

### Step 1: Create a New Design

1. Click **"New"** in the toolbar
2. Choose a location and name for your design asset
3. The design will be created and loaded

### Step 2: Add Elements

1. In the **Hierarchy** panel, select an element type from the dropdown:
   - Panel
   - Button
   - Label
   - TextBox
   - Image

2. Click **"Add"** to create the element
3. The element appears in the hierarchy and on the canvas

### Step 3: Customize Properties

1. Click on an element in the hierarchy to select it
2. In the **Properties** panel, you can edit:
   - **Name**: Element identifier
   - **Position**: X, Y coordinates
   - **Size**: Width, Height
   - **Colors**: Background, Text, Border
   - **Text**: Content (for buttons, labels, textboxes)
   - **Font Size**: Text size
   - **Border Width**: Outline thickness
   - **State**: Visible, Enabled, Interactive

### Step 4: Arrange Elements

- Elements are displayed on the canvas
- Selected elements are highlighted in yellow
- You can see the element type and name above each element
- Use the **Zoom** slider to zoom in/out

### Step 5: Save Your Design

1. Click **"Save"** in the toolbar
2. Your design is saved as a `.asset` file

## 📦 **Using Your Design in Unity**

### Method 1: Auto-Load with GUIDesignLoader

1. Create an empty GameObject in your scene
2. Add the **GUIDesignLoader** component
3. Drag your design asset into the **Design Asset** field
4. Check **Load On Start**
5. Press Play - your GUI appears automatically!

### Method 2: Generate C# Script

1. In the designer, click **"Generate Script"**
2. Save the generated `.cs` file
3. Attach the script to a GameObject
4. Press Play - your GUI is created from code!

**Generated script example:**
```csharp
public class MyGUIDesign : MonoBehaviour
{
    private UnityGUIBridge guiBridge;
    private Button myButton;
    private Label myLabel;
    
    void Start()
    {
        guiBridge = gameObject.AddComponent<UnityGUIBridge>();
        CreateGUI();
    }
    
    void CreateGUI()
    {
        // Auto-generated element creation code
        myButton = new Button("Click Me")
        {
            Position = new Vector2(100, 100),
            Size = new Vector2(200, 50),
            // ... all your customizations
        };
        
        myButton.OnButtonClick += OnMyButtonClick;
        guiBridge.AddElement(myButton);
    }
    
    void OnMyButtonClick(Button button)
    {
        Debug.Log("Button clicked!");
        // Add your logic here
    }
}
```

### Method 3: Manual Script (Full Control)

Use the design as a reference and write your own script with custom logic.

## 🎯 **Designer Features**

### Hierarchy Panel
- **Add Elements**: Select type and click "Add"
- **Select Elements**: Click on element name
- **Delete Elements**: Click the "X" button
- **Parent-Child**: Add elements while another is selected to make it a child

### Canvas Panel
- **Visual Preview**: See your GUI layout
- **Grid**: Helps with alignment
- **Zoom**: Scale the view (0.1x to 2x)
- **Selection**: Yellow outline shows selected element
- **Element Labels**: Type shown above each element

### Properties Panel
- **Basic**: Name, Type
- **Transform**: Position, Size
- **Appearance**: Colors, Border
- **Text**: Content, Color, Size, Alignment
- **Element-Specific**: 
  - TextBox: Placeholder, Password mode
  - Image: Image path
  - Slider: Min/Max values
- **State**: Visible, Enabled, Interactive

## 💡 **Tips & Tricks**

### Organizing Elements
- Use **Panels** as containers for groups of elements
- Name elements descriptively (e.g., "MainMenuPanel", "StartButton")
- Use parent-child relationships for logical grouping

### Styling
- Set background alpha to 0 for transparent elements
- Use border width 0 to remove borders
- Match colors across elements for consistent themes
- Standard font sizes: 12-14 for body, 18-24 for headers

### Layout
- Use the grid for alignment
- Common button size: 200x50
- Common text input size: 200x30
- Leave padding between elements (10-20 pixels)

### Performance
- Don't create too many elements (keep under 50 for best performance)
- Disable interactive on decorative elements
- Set visible=false instead of deleting temporary elements

## 🔧 **Workflow Examples**

### Creating a Main Menu

1. **Create design**: "MainMenu.asset"
2. **Add Panel**: 400x600, centered, dark background
3. **Add Label**: "Main Menu", large font, centered
4. **Add Buttons**: "Start", "Options", "Quit"
5. **Style**: Apply consistent colors
6. **Generate Script**: Get the C# code
7. **Add Logic**: Implement button click handlers

### Creating a HUD

1. **Create design**: "PlayerHUD.asset"
2. **Add Panel**: Health bar background
3. **Add Panel**: Health bar fill (child of background)
4. **Add Label**: Health text
5. **Add Panel**: Ammo display
6. **Position**: Top-left corner
7. **Load at Runtime**: Use GUIDesignLoader

### Creating a Pause Menu

1. **Create design**: "PauseMenu.asset"
2. **Add Panel**: Semi-transparent overlay
3. **Add Label**: "PAUSED"
4. **Add Buttons**: "Resume", "Restart", "Quit"
5. **Generate Script**: Get code
6. **Add Logic**: Toggle visibility on ESC key

## 🐛 **Troubleshooting**

**Designer won't open:**
- Make sure all scripts are compiled without errors
- Check Unity console for error messages

**Elements not showing on canvas:**
- Check if element is visible (visible = true)
- Check if size is greater than 0
- Try zooming out

**Generated script has errors:**
- Make sure all element names are valid (no special characters)
- Check that GameGUI.Core.dll is imported
- Verify all using statements are present

**Design not loading at runtime:**
- Check GUIDesignLoader has the design asset assigned
- Verify "Load On Start" is checked
- Check Unity console for errors

## 📚 **Next Steps**

1. **Experiment**: Try creating different UI layouts
2. **Customize**: Edit generated scripts to add your logic
3. **Theme**: Create consistent color schemes
4. **Iterate**: Design, test, refine

**Happy Designing! 🎨✨**

