using GameGUI.Core;
using System;

namespace GameGUI.Elements
{
    /// <summary>
    /// Progress bar element for showing completion percentage
    /// Super easy to use with automatic animations and styling
    /// </summary>
    public class ProgressBar : GUIElement
    {
        private float _value;
        private float _minValue;
        private float _maxValue;
        private bool _showText;
        private string _customText;
        private ProgressBarStyle _barStyle;
        
        public float Value
        {
            get => _value;
            set
            {
                var newValue = Mathf.Clamp(value, _minValue, _maxValue);
                if (!Mathf.Approximately(_value, newValue))
                {
                    _value = newValue;
                    OnValueChanged?.Invoke(this, _value);
                }
            }
        }
        
        public float MinValue
        {
            get => _minValue;
            set
            {
                _minValue = value;
                Value = _value;
            }
        }
        
        public float MaxValue
        {
            get => _maxValue;
            set
            {
                _maxValue = value;
                Value = _value;
            }
        }
        
        public bool ShowText
        {
            get => _showText;
            set => _showText = value;
        }
        
        public string CustomText
        {
            get => _customText;
            set => _customText = value;
        }
        
        public ProgressBarStyle BarStyle
        {
            get => _barStyle;
            set => _barStyle = value;
        }
        
        public GUIStyle BackgroundStyle { get; set; }
        public GUIStyle FillStyle { get; set; }
        public GUIStyle TextStyle { get; set; }
        
        public event Action<ProgressBar, float> OnValueChanged;
        public event Action<ProgressBar> OnComplete;
        
        public ProgressBar(float minValue = 0f, float maxValue = 100f, float initialValue = 0f)
        {
            _minValue = minValue;
            _maxValue = maxValue;
            _value = Mathf.Clamp(initialValue, minValue, maxValue);
            _showText = true;
            _barStyle = ProgressBarStyle.Solid;
            
            Size = new Vector2(300, 30);
            
            InitializeStyles();
        }
        
        private void InitializeStyles()
        {
            BackgroundStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#E0E0E0"),
                BorderRadius = 4,
                BorderWidth = 1,
                BorderColor = Color.FromHex("#CCCCCC")
            };
            
            FillStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#4CAF50"),
                BorderRadius = 4
            };
            
            TextStyle = new GUIStyle
            {
                TextColor = Color.FromHex("#FFFFFF"),
                FontSize = 14,
                FontStyle = FontStyle.Bold,
                TextAlignment = TextAlignment.Center
            };
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            
            // Draw background
            renderer.DrawRoundedRect(bounds, BackgroundStyle.BorderRadius, BackgroundStyle.BackgroundColor);
            if (BackgroundStyle.BorderWidth > 0)
            {
                renderer.DrawRoundedRectOutline(bounds, BackgroundStyle.BorderRadius,
                    BackgroundStyle.BorderColor, BackgroundStyle.BorderWidth);
            }
            
            // Calculate fill width
            float normalizedValue = GetNormalizedValue();
            float fillWidth = bounds.Width * normalizedValue;
            
            if (fillWidth > 0)
            {
                var fillRect = new Rect(bounds.X, bounds.Y, fillWidth, bounds.Height);
                
                switch (_barStyle)
                {
                    case ProgressBarStyle.Solid:
                        renderer.DrawRoundedRect(fillRect, FillStyle.BorderRadius, FillStyle.BackgroundColor);
                        break;
                    
                    case ProgressBarStyle.Gradient:
                        var startColor = FillStyle.BackgroundColor;
                        var endColor = ColorHelper.Lerp(startColor, Core.Color.FromHex("#FFFFFF"), 0.3f);
                        renderer.DrawGradient(fillRect, endColor, endColor, startColor, startColor);
                        break;
                    
                    case ProgressBarStyle.Striped:
                        DrawStripedFill(renderer, fillRect);
                        break;
                }
            }
            
            // Draw text
            if (_showText)
            {
                string displayText = !string.IsNullOrEmpty(_customText) 
                    ? _customText 
                    : $"{GetPercentage():F0}%";
                
                var textSize = renderer.MeasureText(displayText, TextStyle);
                var textPos = new Vector2(
                    bounds.X + (bounds.Width - textSize.X) * 0.5f,
                    bounds.Y + (bounds.Height - textSize.Y) * 0.5f
                );
                
                renderer.DrawText(displayText, textPos, TextStyle);
            }
        }
        
        private void DrawStripedFill(IGUIRenderer renderer, Rect fillRect)
        {
            // Draw base fill
            renderer.DrawRoundedRect(fillRect, FillStyle.BorderRadius, FillStyle.BackgroundColor);
            
            // Draw stripes
            float stripeWidth = 10f;
            float stripeSpacing = 20f;
            var stripeColor = ColorHelper.Lerp(FillStyle.BackgroundColor, Core.Color.FromHex("#FFFFFF"), 0.2f);
            
            for (float x = fillRect.X; x < fillRect.X + fillRect.Width; x += stripeSpacing)
            {
                var stripeRect = new Rect(x, fillRect.Y, stripeWidth, fillRect.Height);
                renderer.DrawRect(stripeRect, stripeColor);
            }
        }
        
        public float GetNormalizedValue()
        {
            if (Mathf.Approximately(_maxValue, _minValue))
                return 0f;
            return (_value - _minValue) / (_maxValue - _minValue);
        }
        
        public float GetPercentage()
        {
            return GetNormalizedValue() * 100f;
        }
        
        public void SetProgress(float value)
        {
            Value = value;
            
            if (Mathf.Approximately(value, _maxValue))
            {
                OnComplete?.Invoke(this);
            }
        }
        
        public void SetProgressNormalized(float normalizedValue)
        {
            Value = _minValue + Mathf.Clamp01(normalizedValue) * (_maxValue - _minValue);
        }
        
        public void SetProgressPercentage(float percentage)
        {
            SetProgressNormalized(percentage / 100f);
        }
        
        public void Increment(float amount = 1f)
        {
            Value += amount;
        }
        
        public void Reset()
        {
            Value = _minValue;
        }
        
        public void Complete()
        {
            Value = _maxValue;
        }
        
        public bool IsComplete()
        {
            return Mathf.Approximately(_value, _maxValue);
        }
        
        public void SetColor(Core.Color color)
        {
            if (FillStyle != null)
            {
                FillStyle.BackgroundColor = color;
            }
        }

        public void SetColorByProgress()
        {
            float progress = GetNormalizedValue();

            if (progress < 0.33f)
            {
                SetColor(Core.Color.FromHex("#F44336")); // Red
            }
            else if (progress < 0.66f)
            {
                SetColor(Core.Color.FromHex("#FF9800")); // Orange
            }
            else
            {
                SetColor(Core.Color.FromHex("#4CAF50")); // Green
            }
        }
    }

    public enum ProgressBarStyle
    {
        Solid,
        Gradient,
        Striped
    }

    public static class ColorHelper
    {
        public static Core.Color Lerp(Core.Color a, Core.Color b, float t)
        {
            t = Mathf.Clamp01(t);
            return new Core.Color(
                a.R + (b.R - a.R) * t,
                a.G + (b.G - a.G) * t,
                a.B + (b.B - a.B) * t,
                a.A + (b.A - a.A) * t
            );
        }
    }
}

