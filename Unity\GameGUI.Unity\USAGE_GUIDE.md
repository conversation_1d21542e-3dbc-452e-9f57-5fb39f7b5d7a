# GameGUI for Unity - Complete Usage Guide

## 🎯 **Two Ways to Use GameGUI**

### **Method 1: Visual Designer** ⭐ (Recommended - Easy)
Use the built-in Unity editor tool to design your GUI visually

### **Method 2: Code** (Advanced - Full Control)
Write C# scripts manually to create your GUI

---

## 🎨 **Method 1: Using the Visual Designer**

### **Step-by-Step Tutorial**

#### **1. Open the Designer**
```
Unity Menu → Window → GameGUI → GUI Designer
```

A window opens with 3 panels:
- **Left**: Hierarchy (your elements)
- **Center**: Canvas (visual preview)
- **Right**: Properties (customize selected element)

#### **2. Create a New Design**
1. Click **"New"** button in toolbar
2. Save as: `Assets/GUI/MainMenu.asset`
3. Your design is created!

#### **3. Add Elements**
1. In **Hierarchy** panel:
   - Select "Button" from dropdown
   - Click **"Add"**
   - But<PERSON> appears in hierarchy and canvas!

2. Add more elements:
   - Add a **Panel** (container)
   - Add a **Label** (text)
   - Add a **TextBox** (input field)

#### **4. Customize Elements**
1. Click on an element in the hierarchy
2. In **Properties** panel, edit:
   ```
   Name: "StartButton"
   Position: X=100, Y=100
   Size: Width=200, Height=50
   Background Color: Green
   Text: "Start Game"
   Text Color: White
   Font Size: 18
   ```

#### **5. Save Your Design**
Click **"Save"** in toolbar - Done! ✅

#### **6. Use in Your Game - 3 Options:**

##### **Option A: Auto-Load (Easiest)**
1. Create empty GameObject in scene
2. Add component: **GUIDesignLoader**
3. Drag your design asset into **Design Asset** field
4. Check **"Load On Start"**
5. Press Play - GUI appears automatically! 🎉

##### **Option B: Generate Script**
1. In designer, click **"Generate Script"**
2. Save as: `Assets/Scripts/MainMenuGUI.cs`
3. Attach script to GameObject
4. Edit the generated methods to add your logic:
```csharp
void OnStartButtonClick(Button button)
{
    Debug.Log("Start clicked!");
    SceneManager.LoadScene("GameScene");
}
```
5. Press Play!

##### **Option C: Manual Script (Full Control)**
Use the design as reference and write your own script.

---

## 💻 **Method 2: Writing Code Manually**

### **Quick Example - Main Menu**

```csharp
using UnityEngine;
using GameGUI.Unity;
using GameGUI.Elements;
using GameGUI.Core;

public class MainMenu : MonoBehaviour
{
    private UnityGUIBridge guiBridge;
    
    void Start()
    {
        // Initialize GUI system
        guiBridge = gameObject.AddComponent<UnityGUIBridge>();
        
        // Create the menu
        CreateMenu();
    }
    
    void CreateMenu()
    {
        // Create panel
        var panel = new Panel
        {
            Position = new Vector2(100, 100),
            Size = new Vector2(300, 400),
            Style = new GUIStyle
            {
                BackgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.9f),
                BorderColor = Color.White,
                BorderWidth = 2
            }
        };
        guiBridge.AddElement(panel);
        
        // Create title
        var title = new Label("Main Menu")
        {
            Position = new Vector2(0, 20),
            Size = new Vector2(300, 40),
            Style = new GUIStyle
            {
                TextColor = Color.White,
                FontSize = 24,
                TextAlignment = TextAlignment.Center
            }
        };
        guiBridge.AddElement(title, panel);
        
        // Create start button
        var startBtn = new Button("Start Game")
        {
            Position = new Vector2(50, 80),
            Size = new Vector2(200, 50),
            Style = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#4CAF50"),
                TextColor = Color.White
            }
        };
        startBtn.OnButtonClick += (btn) => StartGame();
        guiBridge.AddElement(startBtn, panel);
        
        // Create quit button
        var quitBtn = new Button("Quit")
        {
            Position = new Vector2(50, 140),
            Size = new Vector2(200, 50),
            Style = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#F44336"),
                TextColor = Color.White
            }
        };
        quitBtn.OnButtonClick += (btn) => Application.Quit();
        guiBridge.AddElement(quitBtn, panel);
    }
    
    void StartGame()
    {
        Debug.Log("Starting game...");
        // Load your game scene
    }
}
```

### **Using the Helper (Easier)**

```csharp
using UnityEngine;
using GameGUI.Unity;

public class SimpleMenu : MonoBehaviour
{
    void Start()
    {
        var guiBridge = gameObject.AddComponent<UnityGUIBridge>();
        
        // Create entire menu with one line!
        var menu = UnityGUIHelper.CreateSimpleMenu("Main Menu",
            ("Start Game", StartGame),
            ("Options", ShowOptions),
            ("Quit", Application.Quit)
        );
        
        guiBridge.AddElement(menu);
    }
    
    void StartGame() => Debug.Log("Start!");
    void ShowOptions() => Debug.Log("Options!");
}
```

---

## 🎮 **Common Use Cases**

### **1. Pause Menu**

**Using Designer:**
1. Create design: "PauseMenu.asset"
2. Add semi-transparent panel (400x300)
3. Add label: "PAUSED"
4. Add buttons: Resume, Restart, Quit
5. Generate script
6. Add toggle logic:

```csharp
void Update()
{
    if (Input.GetKeyDown(KeyCode.Escape))
    {
        TogglePause();
    }
}

void TogglePause()
{
    isPaused = !isPaused;
    pausePanel.Visible = isPaused;
    Time.timeScale = isPaused ? 0f : 1f;
}
```

### **2. Player HUD**

**Using Code:**
```csharp
public class PlayerHUD : MonoBehaviour
{
    private Panel healthBar;
    private Label healthText;
    private float health = 100f;
    
    void Start()
    {
        var guiBridge = gameObject.AddComponent<UnityGUIBridge>();
        
        // Health bar background
        var healthBg = new Panel
        {
            Position = new Vector2(20, 20),
            Size = new Vector2(200, 30),
            Style = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#333333"),
                BorderColor = Color.White,
                BorderWidth = 2
            }
        };
        guiBridge.AddElement(healthBg);
        
        // Health bar fill
        healthBar = new Panel
        {
            Position = new Vector2(2, 2),
            Size = new Vector2(196, 26),
            Style = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#4CAF50")
            }
        };
        guiBridge.AddElement(healthBar, healthBg);
        
        // Health text
        healthText = new Label($"{health}/100")
        {
            Position = new Vector2(0, 0),
            Size = new Vector2(200, 30),
            Style = new GUIStyle
            {
                TextColor = Color.White,
                TextAlignment = TextAlignment.Center
            }
        };
        guiBridge.AddElement(healthText, healthBg);
    }
    
    public void TakeDamage(float damage)
    {
        health -= damage;
        UpdateHealthBar();
    }
    
    void UpdateHealthBar()
    {
        float percent = health / 100f;
        healthBar.Size = new Vector2(196 * percent, 26);
        healthText.Text = $"{health}/100";
    }
}
```

### **3. Login Screen**

**Using Designer:**
1. Create design: "LoginScreen.asset"
2. Add panel (300x200)
3. Add label: "Login"
4. Add textbox: "Username"
5. Add textbox: "Password" (set IsPassword = true)
6. Add button: "Login"
7. Generate script
8. Implement login logic

---

## 🎨 **Styling Tips**

### **Colors**
```csharp
// Use hex colors
Color.FromHex("#4CAF50")  // Green
Color.FromHex("#2196F3")  // Blue
Color.FromHex("#F44336")  // Red

// Or RGB
new Color(0.3f, 0.3f, 0.3f, 0.9f)
```

### **Themes**
```csharp
// Apply built-in themes
var darkTheme = GUITheme.CreateDarkTheme();
darkTheme.ApplyToElement(myButton);

var lightTheme = GUITheme.CreateLightTheme();
lightTheme.ApplyToElement(myPanel);
```

### **Common Sizes**
- Button: 200x50
- Text Input: 200x30
- Panel: 400x600 (menu), 300x200 (dialog)
- Label: Auto-width, 30-40 height

---

## 📋 **Comparison: Designer vs Code**

| Feature | Visual Designer | Manual Code |
|---------|----------------|-------------|
| **Ease of Use** | ⭐⭐⭐⭐⭐ Very Easy | ⭐⭐⭐ Moderate |
| **Speed** | ⭐⭐⭐⭐⭐ Very Fast | ⭐⭐⭐ Slower |
| **Flexibility** | ⭐⭐⭐ Good | ⭐⭐⭐⭐⭐ Full Control |
| **Learning Curve** | ⭐⭐⭐⭐⭐ Easy | ⭐⭐⭐ Requires C# knowledge |
| **Best For** | Prototyping, Designers | Complex logic, Programmers |

**Recommendation:** Start with the designer, then customize the generated code!

---

## 🚀 **Getting Started Checklist**

- [ ] Import GameGUI.Core.dll to Unity
- [ ] Copy Unity integration scripts
- [ ] Open designer: Window → GameGUI → GUI Designer
- [ ] Create your first design
- [ ] Add some elements (button, label, panel)
- [ ] Customize colors and text
- [ ] Save the design
- [ ] Use GUIDesignLoader to load it
- [ ] Press Play and see your GUI!
- [ ] Generate script and add your logic

---

## 💡 **Pro Tips**

1. **Use the Designer for layout** - Get positions and sizes right visually
2. **Generate the script** - Get clean, working code
3. **Customize the code** - Add your game logic
4. **Iterate** - Go back to designer to tweak layout

5. **Name elements clearly** - "StartButton" not "Button1"
6. **Use panels as containers** - Group related elements
7. **Apply themes** - Consistent look across your game
8. **Test frequently** - Press Play often to see results

---

## 🎯 **Next Steps**

1. **Try the designer** - Create a simple menu
2. **Load it in Unity** - Use GUIDesignLoader
3. **Generate a script** - See the code
4. **Customize** - Add your game logic
5. **Build your game UI** - Create all your menus!

**You're ready to create amazing game UIs! 🎮✨**

