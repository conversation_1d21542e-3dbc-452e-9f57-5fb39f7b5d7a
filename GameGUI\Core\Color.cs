using System;

namespace GameGUI.Core
{
    /// <summary>
    /// Color structure for GUI styling
    /// </summary>
    [Serializable]
    public struct Color : IEquatable<Color>
    {
        public float R;
        public float G;
        public float B;
        public float A;
        
        public static readonly Color White = new Color(1, 1, 1, 1);
        public static readonly Color Black = new Color(0, 0, 0, 1);
        public static readonly Color Red = new Color(1, 0, 0, 1);
        public static readonly Color Green = new Color(0, 1, 0, 1);
        public static readonly Color Blue = new Color(0, 0, 1, 1);
        public static readonly Color Yellow = new Color(1, 1, 0, 1);
        public static readonly Color Cyan = new Color(0, 1, 1, 1);
        public static readonly Color Magenta = new Color(1, 0, 1, 1);
        public static readonly Color Transparent = new Color(0, 0, 0, 0);
        public static readonly Color Gray = new Color(0.5f, 0.5f, 0.5f, 1);
        
        public Color(float r, float g, float b, float a = 1.0f)
        {
            R = Math.Max(0, Math.Min(1, r));
            G = Math.Max(0, Math.Min(1, g));
            B = Math.Max(0, Math.Min(1, b));
            A = Math.Max(0, Math.Min(1, a));
        }
        
        public Color(byte r, byte g, byte b, byte a = 255)
        {
            R = r / 255.0f;
            G = g / 255.0f;
            B = b / 255.0f;
            A = a / 255.0f;
        }
        
        public static Color FromHex(string hex)
        {
            if (hex.StartsWith("#"))
                hex = hex.Substring(1);
                
            if (hex.Length == 6)
                hex += "FF";
                
            if (hex.Length != 8)
                throw new ArgumentException("Invalid hex color format");
                
            byte r = Convert.ToByte(hex.Substring(0, 2), 16);
            byte g = Convert.ToByte(hex.Substring(2, 2), 16);
            byte b = Convert.ToByte(hex.Substring(4, 2), 16);
            byte a = Convert.ToByte(hex.Substring(6, 2), 16);
            
            return new Color(r, g, b, a);
        }
        
        public string ToHex()
        {
            byte r = (byte)(R * 255);
            byte g = (byte)(G * 255);
            byte b = (byte)(B * 255);
            byte a = (byte)(A * 255);
            return $"#{r:X2}{g:X2}{b:X2}{a:X2}";
        }
        
        public static Color Lerp(Color a, Color b, float t)
        {
            t = Math.Max(0, Math.Min(1, t));
            return new Color(
                a.R + (b.R - a.R) * t,
                a.G + (b.G - a.G) * t,
                a.B + (b.B - a.B) * t,
                a.A + (b.A - a.A) * t
            );
        }
        
        public static Color operator *(Color color, float scalar)
        {
            return new Color(color.R * scalar, color.G * scalar, color.B * scalar, color.A);
        }
        
        public static bool operator ==(Color a, Color b) => a.Equals(b);
        public static bool operator !=(Color a, Color b) => !a.Equals(b);
        
        public bool Equals(Color other)
        {
            return Math.Abs(R - other.R) < float.Epsilon &&
                   Math.Abs(G - other.G) < float.Epsilon &&
                   Math.Abs(B - other.B) < float.Epsilon &&
                   Math.Abs(A - other.A) < float.Epsilon;
        }
        
        public override bool Equals(object obj) => obj is Color other && Equals(other);
        public override int GetHashCode() => HashCode.Combine(R, G, B, A);
        public override string ToString() => $"Color({R:F2}, {G:F2}, {B:F2}, {A:F2})";
    }
}
