# GameGUI - Pure C# Game UI System for Unity

GameGUI is a powerful, flexible GUI system built in pure C# designed specifically for Unity game development. It provides a clean, Unity-friendly interface that can be easily integrated into any Unity project without external dependencies.

## Features

- **Pure C# Core**: No Unity dependencies in the core library - can be used in any C# project
- **Unity Integration**: Seamless Unity integration with MonoBehaviour bridge components
- **Immediate Mode Rendering**: Efficient rendering using Unity's immediate mode GUI
- **Flexible Layout System**: Multiple layout managers (Stack, Grid, Absolute positioning)
- **Rich Element Library**: Buttons, Labels, TextBoxes, Images, Panels, and more
- **Theming Support**: Complete theming system with dark/light themes and custom themes
- **Event System**: Comprehensive input handling and event management
- **Extensible**: Easy to create custom elements and layouts
- **Unity Package**: Distributed as a proper Unity package with samples and documentation

## Quick Start

### Installation

1. **Option A: Unity Package Manager**
   - Copy the `Unity/GameGUI.Unity` folder to your Unity project's `Packages` folder
   - Unity will automatically detect and import the package

2. **Option B: Manual Installation**
   - Build the `GameGUI.Core.dll` from the C# project
   - Copy the DLL and the Unity integration scripts to your Unity project

### Basic Usage

1. Create a GameObject and add the `UnityGUIBridge` component
2. Start creating your UI:

```csharp
using GameGUI.Unity;
using GameGUI.Elements;
using GameGUI.Core;

public class MyGUI : MonoBehaviour
{
    private UnityGUIBridge guiBridge;

    void Start()
    {
        guiBridge = GetComponent<UnityGUIBridge>();
        CreateUI();
    }

    void CreateUI()
    {
        // Create a button using helper
        var button = UnityGUIHelper.CreateButton(
            "Click Me!",
            new Vector2(100, 100),
            new Vector2(120, 40),
            () => Debug.Log("Button clicked!")
        );

        guiBridge.AddElement(button);

        // Create a text input
        var textBox = UnityGUIHelper.CreateTextBox(
            "Enter text...",
            new Vector2(100, 150),
            new Vector2(200, 30)
        );

        guiBridge.AddElement(textBox);
    }
}
```

## Core Components

### Elements

GameGUI provides several built-in UI elements:

- **Panel**: Container element for grouping other elements
- **Button**: Interactive button with hover/press states
- **Label**: Text display element
- **TextBox**: Text input element with cursor and selection
- **Image**: Image display with various scaling modes

### Layout System

Automatic layout management with multiple layout types:

- **StackLayout**: Arrange elements vertically or horizontally
- **GridLayout**: Arrange elements in a grid pattern
- **Absolute**: Manual positioning (default)

```csharp
// Create a vertical stack layout
var panel = new Panel();
var stackLayout = new StackLayout(StackDirection.Vertical, spacing: 10);

// Add elements to the panel
panel.AddChild(new Button("Button 1"));
panel.AddChild(new Button("Button 2"));
panel.AddChild(new Button("Button 3"));

// Apply layout
stackLayout.CalculateLayout(panel, panel.Children);
```

### Theming

Complete theming support with predefined themes:

```csharp
// Apply a dark theme
var darkTheme = GUITheme.CreateDarkTheme();
darkTheme.ApplyToElement(myButton);

// Create custom theme
var customTheme = new GUITheme("Custom")
{
    Colors = new ThemeColors
    {
        Primary = Color.FromHex("#FF6B6B"),
        Background = Color.FromHex("#2C3E50")
    }
};
```

## Architecture

### Core Interfaces

- `IGUIElement`: Base interface for all UI elements
- `IGUIRenderer`: Abstraction for rendering to different backends
- `ILayoutManager`: Interface for layout management systems

### Event System

Comprehensive event handling for user interactions:

```csharp
// Mouse events
element.OnClick += (el) => Console.WriteLine("Clicked!");
element.OnHover += (el) => Console.WriteLine("Hovered!");

// Focus events
element.OnFocus += (el) => Console.WriteLine("Focused!");
element.OnBlur += (el) => Console.WriteLine("Lost focus!");
```

## Advanced Usage

### Custom Elements

Create custom UI elements by inheriting from `GUIElement`:

```csharp
public class ProgressBar : GUIElement
{
    private float _value = 0f;
    
    public float Value
    {
        get => _value;
        set => _value = Math.Max(0, Math.Min(1, value));
    }
    
    protected override void OnRender(IGUIRenderer renderer)
    {
        var bounds = GetBounds();
        
        // Draw background
        renderer.DrawRect(bounds, Style.BackgroundColor);
        
        // Draw progress
        var progressWidth = bounds.Width * Value;
        var progressRect = new Rect(bounds.X, bounds.Y, progressWidth, bounds.Height);
        renderer.DrawRect(progressRect, Style.BorderColor);
    }
}
```

### Custom Layout Managers

Implement `ILayoutManager` to create custom layout behaviors:

```csharp
public class CircularLayout : LayoutManager
{
    public float Radius { get; set; } = 100f;
    
    public override void CalculateLayout(IGUIElement container, IList<IGUIElement> children)
    {
        var center = container.GetBounds().Center;
        var angleStep = 360f / children.Count;
        
        for (int i = 0; i < children.Count; i++)
        {
            var angle = i * angleStep * Mathf.Deg2Rad;
            var x = center.X + Mathf.Cos(angle) * Radius;
            var y = center.Y + Mathf.Sin(angle) * Radius;
            
            children[i].Position = new Vector2(x, y);
        }
    }
}
```

## Project Structure

```
GameGUI/
├── GameGUI.Core/           # Pure C# core library (no Unity dependencies)
│   ├── Core/              # Core interfaces and base classes
│   ├── Elements/          # UI element implementations
│   ├── Layout/            # Layout management system
│   ├── Events/            # Event handling system
│   └── Theming/           # Theme and styling system
│
└── Unity/
    └── GameGUI.Unity/      # Unity package
        ├── Runtime/        # Unity integration scripts
        ├── Samples~/       # Example scenes and scripts
        └── package.json    # Unity package definition
```

## Unity Integration

The Unity integration is built as a bridge between the pure C# core and Unity:

- **UnityGUIBridge**: Main MonoBehaviour component that manages the GUI system
- **UnityRenderer**: Implements IGUIRenderer using Unity's immediate mode GUI
- **UnityGUIHelper**: Utility methods for easy Unity integration
- **Examples**: Complete sample scenes showing various features

## Performance Considerations

- Efficient rendering with clipping and culling
- Minimal allocations during runtime
- Cached text measurements and resources
- Optimized event propagation

## Examples

See the `GameGUI/Examples` folder for complete examples:
- Basic UI creation
- Layout management
- Theming and styling
- Custom elements
- Event handling

## License

This project is open source and available under the MIT License.

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.
