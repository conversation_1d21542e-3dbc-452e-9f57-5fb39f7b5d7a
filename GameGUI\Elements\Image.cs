using System;
using GameGUI.Core;

namespace GameGUI.Elements
{
    /// <summary>
    /// Image display element
    /// </summary>
    public class Image : GUIElement
    {
        private string _imagePath = string.Empty;
        private object? _imageResource;
        private Core.Color _tint = Core.Color.White;
        private ImageScaleMode _scaleMode = ImageScaleMode.Stretch;
        private bool _preserveAspect = true;

        public string ImagePath
        {
            get => _imagePath;
            set
            {
                if (_imagePath != value)
                {
                    _imagePath = value ?? string.Empty;
                    _imageResource = null; // Clear cached resource
                    OnImagePathChanged();
                }
            }
        }

        public object? ImageResource
        {
            get => _imageResource;
            set
            {
                if (_imageResource != value)
                {
                    _imageResource = value;
                    if (value != null)
                        _imagePath = string.Empty; // Clear path when using direct resource
                    OnImageResourceChanged();
                }
            }
        }

        public Core.Color TintColor
        {
            get => _tint;
            set
            {
                if (_tint != value)
                {
                    _tint = value;
                    OnTintChanged();
                }
            }
        }
        
        public ImageScaleMode ScaleMode
        {
            get => _scaleMode;
            set
            {
                if (_scaleMode != value)
                {
                    _scaleMode = value;
                    OnScaleModeChanged();
                }
            }
        }
        
        public bool PreserveAspect
        {
            get => _preserveAspect;
            set
            {
                if (_preserveAspect != value)
                {
                    _preserveAspect = value;
                    OnPreserveAspectChanged();
                }
            }
        }
        
        public Image()
        {
            Style.BackgroundColor = Color.Transparent;
            Interactive = false;
        }
        
        public Image(string imagePath) : this()
        {
            ImagePath = imagePath;
        }
        
        public Image(object imageResource) : this()
        {
            ImageResource = imageResource;
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            
            // Draw background if visible
            if (Style.BackgroundColor.A > 0)
            {
                renderer.DrawRect(bounds, Style.BackgroundColor);
            }
            
            // Draw image
            if (HasImage())
            {
                var imageRect = CalculateImageRect(bounds);
                
                if (!string.IsNullOrEmpty(ImagePath))
                {
                    renderer.DrawImage(ImagePath, imageRect, null, TintColor);
                }
                else if (ImageResource != null)
                {
                    renderer.DrawImage(ImageResource, imageRect, null, TintColor);
                }
            }
            
            // Draw border if specified
            if (Style.BorderWidth > 0 && Style.BorderColor.A > 0)
            {
                renderer.DrawRectOutline(bounds, Style.BorderColor, Style.BorderWidth);
            }
        }
        
        private bool HasImage()
        {
            return !string.IsNullOrEmpty(ImagePath) || ImageResource != null;
        }
        
        private Rect CalculateImageRect(Rect bounds)
        {
            var contentBounds = new Rect(
                bounds.X + Style.Padding.Left,
                bounds.Y + Style.Padding.Top,
                bounds.Width - Style.Padding.Left - Style.Padding.Right,
                bounds.Height - Style.Padding.Top - Style.Padding.Bottom
            );
            
            switch (ScaleMode)
            {
                case ImageScaleMode.Stretch:
                    return contentBounds;
                    
                case ImageScaleMode.ScaleToFit:
                    return CalculateScaleToFitRect(contentBounds);
                    
                case ImageScaleMode.ScaleToFill:
                    return CalculateScaleToFillRect(contentBounds);
                    
                case ImageScaleMode.Center:
                    return CalculateCenterRect(contentBounds);
                    
                default:
                    return contentBounds;
            }
        }
        
        private Rect CalculateScaleToFitRect(Rect bounds)
        {
            // This would require knowing the actual image dimensions
            // For now, return the bounds (same as stretch)
            // In a real implementation, you'd get image dimensions from the renderer
            return bounds;
        }
        
        private Rect CalculateScaleToFillRect(Rect bounds)
        {
            // This would require knowing the actual image dimensions
            // For now, return the bounds (same as stretch)
            return bounds;
        }
        
        private Rect CalculateCenterRect(Rect bounds)
        {
            // This would require knowing the actual image dimensions
            // For now, center a default size within the bounds
            var imageSize = new Vector2(Math.Min(bounds.Width, 100), Math.Min(bounds.Height, 100));
            var x = bounds.X + (bounds.Width - imageSize.X) * 0.5f;
            var y = bounds.Y + (bounds.Height - imageSize.Y) * 0.5f;
            
            return new Rect(x, y, imageSize.X, imageSize.Y);
        }
        
        protected virtual void OnImagePathChanged() { }
        protected virtual void OnImageResourceChanged() { }
        protected virtual void OnTintChanged() { }
        protected virtual void OnScaleModeChanged() { }
        protected virtual void OnPreserveAspectChanged() { }
    }
    
    public enum ImageScaleMode
    {
        /// <summary>
        /// Stretch the image to fill the entire element
        /// </summary>
        Stretch,
        
        /// <summary>
        /// Scale the image to fit within the element while preserving aspect ratio
        /// </summary>
        ScaleToFit,
        
        /// <summary>
        /// Scale the image to fill the element while preserving aspect ratio (may crop)
        /// </summary>
        ScaleToFill,
        
        /// <summary>
        /// Display the image at its original size, centered in the element
        /// </summary>
        Center
    }
}
