using System;
using System.Collections.Generic;
using System.Linq;

namespace GameGUI.Core
{
    /// <summary>
    /// Base implementation of IGUIElement
    /// </summary>
    public abstract class GUIElement : IGUIElement
    {
        private readonly List<IGUIElement> _children = new List<IGUIElement>();
        private IGUIElement _parent;
        private Vector2 _position = Vector2.Zero;
        private Vector2 _size = new Vector2(100, 30);
        private Vector2 _anchor = Vector2.Zero;
        private Vector2 _pivot = Vector2.Zero;
        private float _rotation = 0;
        private Vector2 _scale = Vector2.One;
        private bool _visible = true;
        private bool _enabled = true;
        private bool _interactive = true;
        private int _zOrder = 0;
        private GUIStyle _style;
        
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        
        public IGUIElement Parent
        {
            get => _parent;
            set => SetParent(value);
        }
        
        public IList<IGUIElement> Children => _children.AsReadOnly();
        
        public Vector2 Position
        {
            get => _position;
            set
            {
                if (_position != value)
                {
                    _position = value;
                    OnPositionChanged();
                }
            }
        }
        
        public Vector2 Size
        {
            get => _size;
            set
            {
                if (_size != value)
                {
                    _size = value;
                    OnSizeChanged();
                }
            }
        }
        
        public Vector2 Anchor
        {
            get => _anchor;
            set
            {
                if (_anchor != value)
                {
                    _anchor = value;
                    OnAnchorChanged();
                }
            }
        }
        
        public Vector2 Pivot
        {
            get => _pivot;
            set
            {
                if (_pivot != value)
                {
                    _pivot = value;
                    OnPivotChanged();
                }
            }
        }
        
        public float Rotation
        {
            get => _rotation;
            set
            {
                if (Math.Abs(_rotation - value) > float.Epsilon)
                {
                    _rotation = value;
                    OnRotationChanged();
                }
            }
        }
        
        public Vector2 Scale
        {
            get => _scale;
            set
            {
                if (_scale != value)
                {
                    _scale = value;
                    OnScaleChanged();
                }
            }
        }
        
        public bool Visible
        {
            get => _visible;
            set
            {
                if (_visible != value)
                {
                    _visible = value;
                    OnVisibilityChanged();
                }
            }
        }
        
        public bool Enabled
        {
            get => _enabled;
            set
            {
                if (_enabled != value)
                {
                    _enabled = value;
                    OnEnabledChanged();
                }
            }
        }
        
        public bool Interactive
        {
            get => _interactive;
            set
            {
                if (_interactive != value)
                {
                    _interactive = value;
                    OnInteractiveChanged();
                }
            }
        }
        
        public int ZOrder
        {
            get => _zOrder;
            set
            {
                if (_zOrder != value)
                {
                    _zOrder = value;
                    OnZOrderChanged();
                }
            }
        }
        
        public GUIStyle Style
        {
            get => _style ??= new GUIStyle();
            set => _style = value;
        }
        
        // Events
        public event Action<IGUIElement> OnClick;
        public event Action<IGUIElement> OnHover;
        public event Action<IGUIElement> OnFocus;
        public event Action<IGUIElement> OnBlur;
        
        protected GUIElement()
        {
            Name = GetType().Name;
        }
        
        public virtual void AddChild(IGUIElement child)
        {
            if (child == null || _children.Contains(child)) return;
            
            child.SetParent(this);
            _children.Add(child);
            _children.Sort((a, b) => a.ZOrder.CompareTo(b.ZOrder));
            OnChildAdded(child);
        }
        
        public virtual void RemoveChild(IGUIElement child)
        {
            if (child == null || !_children.Contains(child)) return;
            
            _children.Remove(child);
            child.SetParent(null);
            OnChildRemoved(child);
        }
        
        public virtual void SetParent(IGUIElement parent)
        {
            if (_parent == parent) return;
            
            var oldParent = _parent;
            _parent = parent;
            OnParentChanged(oldParent, parent);
        }
        
        public virtual void Update(float deltaTime)
        {
            if (!Visible) return;
            
            OnUpdate(deltaTime);
            
            foreach (var child in _children.ToList())
            {
                child.Update(deltaTime);
            }
        }
        
        public virtual void Render(IGUIRenderer renderer)
        {
            if (!Visible) return;
            
            var bounds = GetBounds();
            renderer.SetClipRect(bounds);
            
            OnRender(renderer);
            
            foreach (var child in _children.OrderBy(c => c.ZOrder))
            {
                child.Render(renderer);
            }
            
            renderer.ClearClipRect();
        }
        
        public virtual bool ContainsPoint(Vector2 point)
        {
            return GetBounds().Contains(point);
        }
        
        public virtual void CalculateLayout()
        {
            OnCalculateLayout();
            
            foreach (var child in _children)
            {
                child.CalculateLayout();
            }
        }
        
        public virtual Rect GetBounds()
        {
            var worldPos = GetWorldPosition();
            return new Rect(worldPos, Size);
        }
        
        public virtual Vector2 GetWorldPosition()
        {
            var worldPos = Position;
            
            if (Parent != null)
            {
                var parentWorldPos = Parent.GetWorldPosition();
                var parentSize = Parent.Size;
                
                // Apply anchor
                worldPos += new Vector2(parentSize.X * Anchor.X, parentSize.Y * Anchor.Y);
                worldPos += parentWorldPos;
            }
            
            // Apply pivot
            worldPos -= new Vector2(Size.X * Pivot.X, Size.Y * Pivot.Y);
            
            return worldPos;
        }
        
        // Virtual methods for derived classes
        protected virtual void OnUpdate(float deltaTime) { }
        protected virtual void OnRender(IGUIRenderer renderer) { }
        protected virtual void OnCalculateLayout() { }
        protected virtual void OnPositionChanged() { }
        protected virtual void OnSizeChanged() { }
        protected virtual void OnAnchorChanged() { }
        protected virtual void OnPivotChanged() { }
        protected virtual void OnRotationChanged() { }
        protected virtual void OnScaleChanged() { }
        protected virtual void OnVisibilityChanged() { }
        protected virtual void OnEnabledChanged() { }
        protected virtual void OnInteractiveChanged() { }
        protected virtual void OnZOrderChanged() { }
        protected virtual void OnChildAdded(IGUIElement child) { }
        protected virtual void OnChildRemoved(IGUIElement child) { }
        protected virtual void OnParentChanged(IGUIElement oldParent, IGUIElement newParent) { }
        
        // Event triggers (public to implement interface)
        public virtual void TriggerClick() => OnClick?.Invoke(this);
        public virtual void TriggerHover() => OnHover?.Invoke(this);
        public virtual void TriggerFocus() => OnFocus?.Invoke(this);
        public virtual void TriggerBlur() => OnBlur?.Invoke(this);
    }
}
