{"name": "com.gamegui.unity", "version": "1.0.0", "displayName": "GameGUI for Unity", "description": "A powerful, flexible GUI system designed specifically for Unity game development. Pure C# implementation with Unity integration.", "unity": "2019.4", "unityRelease": "0f1", "documentationUrl": "https://github.com/yourusername/GameGUI/blob/main/README.md", "changelogUrl": "https://github.com/yourusername/GameGUI/blob/main/CHANGELOG.md", "licensesUrl": "https://github.com/yourusername/GameGUI/blob/main/LICENSE.md", "keywords": ["gui", "ui", "interface", "game", "unity", "immediate-mode"], "author": {"name": "GameGUI Contributors", "email": "<EMAIL>", "url": "https://github.com/yourusername/GameGUI"}, "type": "library", "hideInEditor": false, "testables": ["com.gamegui.unity"], "samples": [{"displayName": "Basic GUI Examples", "description": "Basic examples showing how to create and use GUI elements", "path": "Samples~/BasicExamples"}, {"displayName": "Advanced GUI Examples", "description": "Advanced examples with custom elements and layouts", "path": "Samples~/AdvancedExamples"}]}