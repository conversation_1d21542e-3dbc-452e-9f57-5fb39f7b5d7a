using System;
using GameGUI.Core;

namespace GameGUI.Elements
{
    /// <summary>
    /// Text label element
    /// </summary>
    public class Label : GUIElement
    {
        private string _text = string.Empty;
        private bool _autoSize = false;
        private bool _wordWrap = false;
        
        public string Text
        {
            get => _text;
            set
            {
                if (_text != value)
                {
                    _text = value ?? string.Empty;
                    OnTextChanged();
                    if (AutoSize)
                    {
                        UpdateAutoSize();
                    }
                }
            }
        }
        
        public bool AutoSize
        {
            get => _autoSize;
            set
            {
                if (_autoSize != value)
                {
                    _autoSize = value;
                    if (_autoSize)
                    {
                        UpdateAutoSize();
                    }
                }
            }
        }
        
        public bool WordWrap
        {
            get => _wordWrap;
            set
            {
                if (_wordWrap != value)
                {
                    _wordWrap = value;
                    OnWordWrapChanged();
                }
            }
        }
        
        public Label()
        {
            Style.BackgroundColor = Color.Transparent;
            Style.TextColor = Color.Black;
            Style.FontSize = 12;
            Style.TextAlignment = TextAlignment.Left;
            Style.TextVerticalAlignment = TextVerticalAlignment.Top;
            Interactive = false;
        }
        
        public Label(string text) : this()
        {
            Text = text;
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            var style = Style;
            
            // Draw background if visible
            if (style.BackgroundColor.A > 0)
            {
                renderer.DrawRect(bounds, style.BackgroundColor);
            }
            
            // Draw text
            if (!string.IsNullOrEmpty(Text))
            {
                var textPosition = CalculateTextPosition(bounds, style);
                renderer.DrawText(Text, textPosition, style);
            }
        }
        
        private Vector2 CalculateTextPosition(Rect bounds, GUIStyle style)
        {
            var textSize = GetTextSize();
            var contentBounds = new Rect(
                bounds.X + style.Padding.Left,
                bounds.Y + style.Padding.Top,
                bounds.Width - style.Padding.Left - style.Padding.Right,
                bounds.Height - style.Padding.Top - style.Padding.Bottom
            );
            
            float x = contentBounds.X;
            float y = contentBounds.Y;
            
            // Horizontal alignment
            switch (style.TextAlignment)
            {
                case TextAlignment.Center:
                    x = contentBounds.X + (contentBounds.Width - textSize.X) * 0.5f;
                    break;
                case TextAlignment.Right:
                    x = contentBounds.X + contentBounds.Width - textSize.X;
                    break;
            }
            
            // Vertical alignment
            switch (style.TextVerticalAlignment)
            {
                case TextVerticalAlignment.Middle:
                    y = contentBounds.Y + (contentBounds.Height - textSize.Y) * 0.5f;
                    break;
                case TextVerticalAlignment.Bottom:
                    y = contentBounds.Y + contentBounds.Height - textSize.Y;
                    break;
            }
            
            return new Vector2(x, y);
        }
        
        private Vector2 GetTextSize()
        {
            // This would need to be implemented by the renderer
            // For now, return an estimated size
            if (string.IsNullOrEmpty(Text))
                return Vector2.Zero;
                
            // Rough estimation: average character width * length, font size for height
            float charWidth = Style.FontSize * 0.6f;
            float lineHeight = Style.FontSize * 1.2f;
            
            if (WordWrap)
            {
                // Calculate wrapped text size
                var availableWidth = Size.X - Style.Padding.Left - Style.Padding.Right;
                var charsPerLine = (int)(availableWidth / charWidth);
                var lines = (Text.Length + charsPerLine - 1) / charsPerLine;
                return new Vector2(Math.Min(Text.Length * charWidth, availableWidth), lines * lineHeight);
            }
            else
            {
                return new Vector2(Text.Length * charWidth, lineHeight);
            }
        }
        
        private void UpdateAutoSize()
        {
            if (!AutoSize) return;
            
            var textSize = GetTextSize();
            Size = new Vector2(
                textSize.X + Style.Padding.Left + Style.Padding.Right,
                textSize.Y + Style.Padding.Top + Style.Padding.Bottom
            );
        }
        
        protected virtual void OnTextChanged() { }
        protected virtual void OnWordWrapChanged() { }
    }
}
