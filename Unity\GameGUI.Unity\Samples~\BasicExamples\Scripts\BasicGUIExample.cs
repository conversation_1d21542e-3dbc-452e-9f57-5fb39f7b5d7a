using GameGUI.Core;
using GameGUI.Elements;
using GameGUI.Theming;
using GameGUI.Unity;
using UnityEngine;

namespace GameGUI.Unity.Examples
{
    /// <summary>
    /// Basic example showing how to use GameGUI in Unity
    /// </summary>
    public class BasicGUIExample : MonoBehaviour
    {
        [Header("Example Settings")]
        [SerializeField] private bool _useDarkTheme = true;
        [SerializeField] private bool _showFPS = true;
        
        private UnityGUIBridge _guiBridge;
        private GUITheme _currentTheme;
        private Label _fpsLabel;
        private Label _outputLabel;
        private int _clickCount = 0;
        
        private void Start()
        {
            SetupGUI();
            CreateExampleUI();
        }
        
        private void SetupGUI()
        {
            // Setup GUI bridge
            _guiBridge = UnityGUIHelper.SetupGUIBridge(gameObject, new Vector2(1920, 1080));
            
            // Setup theme
            _currentTheme = _useDarkTheme ? GUITheme.CreateDarkTheme() : GUITheme.CreateLightTheme();
        }
        
        private void CreateExampleUI()
        {
            CreateMainMenu();
            
            if (_showFPS)
            {
                CreateFPSDisplay();
            }
        }
        
        private void CreateMainMenu()
        {
            // Create main panel
            var mainPanel = UnityGUIHelper.CreatePanel(
                new Vector2(100, 100),
                new Vector2(400, 300),
                new Color(0.1f, 0.1f, 0.1f, 0.9f)
            );
            
            mainPanel.Style.BorderColor = GameGUI.Core.Color.White;
            mainPanel.Style.BorderWidth = 2;
            mainPanel.Style.BorderRadius = 8;
            mainPanel.Style.Padding = new Padding(20);
            
            _guiBridge.AddElement(mainPanel);
            
            // Title
            var titleLabel = UnityGUIHelper.CreateLabel(
                "GameGUI Unity Example",
                Vector2.zero,
                new Vector2(360, 40)
            );
            titleLabel.Style.TextColor = GameGUI.Core.Color.White;
            titleLabel.Style.FontSize = 18;
            titleLabel.Style.FontStyle = FontStyle.Bold;
            titleLabel.Style.TextAlignment = TextAlignment.Center;
            _guiBridge.AddElement(titleLabel, mainPanel);
            
            // Click counter button
            var clickButton = UnityGUIHelper.CreateButton(
                "Click Me!",
                new Vector2(0, 60),
                new Vector2(120, 35),
                OnClickButtonPressed
            );
            _currentTheme.ApplyToElement(clickButton);
            _guiBridge.AddElement(clickButton, mainPanel);
            
            // Theme toggle button
            var themeButton = UnityGUIHelper.CreateButton(
                _useDarkTheme ? "Light Theme" : "Dark Theme",
                new Vector2(130, 60),
                new Vector2(120, 35),
                OnThemeTogglePressed
            );
            _currentTheme.ApplyToElement(themeButton);
            _guiBridge.AddElement(themeButton, mainPanel);
            
            // Text input
            var textBox = UnityGUIHelper.CreateTextBox(
                "Enter text here...",
                new Vector2(0, 110),
                new Vector2(250, 30)
            );
            _currentTheme.ApplyToElement(textBox);
            textBox.OnTextChanged += OnTextChanged;
            _guiBridge.AddElement(textBox, mainPanel);
            
            // Output label
            _outputLabel = UnityGUIHelper.CreateLabel(
                "Output will appear here",
                new Vector2(0, 150),
                new Vector2(360, 60)
            );
            _outputLabel.Style.TextColor = GameGUI.Core.Color.FromHex("#CCCCCC");
            _outputLabel.Style.BackgroundColor = GameGUI.Core.Color.FromHex("#2A2A2A");
            _outputLabel.Style.BorderColor = GameGUI.Core.Color.FromHex("#555555");
            _outputLabel.Style.BorderWidth = 1;
            _outputLabel.Style.Padding = new Padding(8);
            _outputLabel.Style.TextVerticalAlignment = TextVerticalAlignment.Top;
            _guiBridge.AddElement(_outputLabel, mainPanel);
            
            // Quit button
            var quitButton = UnityGUIHelper.CreateButton(
                "Quit",
                new Vector2(0, 230),
                new Vector2(80, 30),
                OnQuitPressed
            );
            quitButton.Style.BackgroundColor = GameGUI.Core.Color.FromHex("#E74C3C");
            quitButton.Style.HoverStyle = new GUIStyle { BackgroundColor = GameGUI.Core.Color.FromHex("#C0392B") };
            _guiBridge.AddElement(quitButton, mainPanel);
        }
        
        private void CreateFPSDisplay()
        {
            _fpsLabel = UnityGUIHelper.CreateLabel(
                "FPS: 0",
                new Vector2(10, 10),
                new Vector2(100, 25)
            );
            _fpsLabel.Style.TextColor = GameGUI.Core.Color.Yellow;
            _fpsLabel.Style.FontSize = 14;
            _fpsLabel.Style.FontStyle = FontStyle.Bold;
            _fpsLabel.Style.BackgroundColor = GameGUI.Core.Color.FromHex("#000000AA");
            _fpsLabel.Style.Padding = new Padding(5);
            
            _guiBridge.AddElement(_fpsLabel);
        }
        
        private void Update()
        {
            UpdateFPS();
        }
        
        private void UpdateFPS()
        {
            if (_fpsLabel != null)
            {
                var fps = Mathf.RoundToInt(1.0f / Time.deltaTime);
                _fpsLabel.Text = $"FPS: {fps}";
                
                // Color code FPS
                if (fps >= 60)
                    _fpsLabel.Style.TextColor = GameGUI.Core.Color.Green;
                else if (fps >= 30)
                    _fpsLabel.Style.TextColor = GameGUI.Core.Color.Yellow;
                else
                    _fpsLabel.Style.TextColor = GameGUI.Core.Color.Red;
            }
        }
        
        private void OnClickButtonPressed()
        {
            _clickCount++;
            UpdateOutput($"Button clicked {_clickCount} times!");
            
            // Find and update the button text
            var button = _guiBridge.FindElement<Button>("Click Me!");
            if (button != null)
            {
                button.Text = $"Clicked {_clickCount}x";
            }
        }
        
        private void OnThemeTogglePressed()
        {
            _useDarkTheme = !_useDarkTheme;
            _currentTheme = _useDarkTheme ? GUITheme.CreateDarkTheme() : GUITheme.CreateLightTheme();
            
            // Apply theme to all elements
            ApplyThemeToAllElements();
            
            // Update button text
            var themeButton = _guiBridge.FindElement<Button>(_useDarkTheme ? "Light Theme" : "Dark Theme");
            if (themeButton != null)
            {
                themeButton.Text = _useDarkTheme ? "Light Theme" : "Dark Theme";
            }
            
            UpdateOutput($"Switched to {_currentTheme.Name} theme");
        }
        
        private void OnTextChanged(TextBox textBox, string newText)
        {
            if (!string.IsNullOrEmpty(newText) && newText != "Enter text here...")
            {
                UpdateOutput($"Text input: '{newText}'");
            }
        }
        
        private void OnQuitPressed()
        {
            UpdateOutput("Quitting application...");
            
            #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
            #else
            Application.Quit();
            #endif
        }
        
        private void UpdateOutput(string message)
        {
            if (_outputLabel != null)
            {
                _outputLabel.Text = message;
                Debug.Log($"[GameGUI] {message}");
            }
        }
        
        private void ApplyThemeToAllElements()
        {
            foreach (var element in _guiBridge.GUIManager.GetAllElements())
            {
                // Skip certain elements that have custom styling
                if (element == _fpsLabel || element == _outputLabel)
                    continue;
                    
                _currentTheme.ApplyToElement(element);
            }
        }
        
        // Unity Editor helpers
        #if UNITY_EDITOR
        [UnityEditor.MenuItem("GameGUI/Create Basic Example")]
        private static void CreateBasicExample()
        {
            var go = new GameObject("GameGUI Basic Example");
            go.AddComponent<BasicGUIExample>();
            UnityEditor.Selection.activeGameObject = go;
        }
        #endif
    }
}
