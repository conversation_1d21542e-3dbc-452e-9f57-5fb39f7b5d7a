using GameGUI.Core;
using GameGUI.Elements;
using GameGUI.Theming;
using UnityEngine;

namespace GameGUI.Unity
{
    /// <summary>
    /// Helper class with utility methods for Unity GUI integration
    /// </summary>
    public static class UnityGUIHelper
    {
        /// <summary>
        /// Convert Unity Vector2 to GameGUI Vector2
        /// </summary>
        public static GameGUI.Core.Vector2 ToGameGUIVector2(UnityEngine.Vector2 unityVector)
        {
            return new GameGUI.Core.Vector2(unityVector.x, unityVector.y);
        }
        
        /// <summary>
        /// Convert GameGUI Vector2 to Unity Vector2
        /// </summary>
        public static UnityEngine.Vector2 ToUnityVector2(GameGUI.Core.Vector2 gameGuiVector)
        {
            return new UnityEngine.Vector2(gameGuiVector.X, gameGuiVector.Y);
        }
        
        /// <summary>
        /// Convert Unity Color to GameGUI Color
        /// </summary>
        public static GameGUI.Core.Color ToGameGUIColor(UnityEngine.Color unityColor)
        {
            return new GameGUI.Core.Color(unityColor.r, unityColor.g, unityColor.b, unityColor.a);
        }
        
        /// <summary>
        /// Convert GameGUI Color to Unity Color
        /// </summary>
        public static UnityEngine.Color ToUnityColor(GameGUI.Core.Color gameGuiColor)
        {
            return new UnityEngine.Color(gameGuiColor.R, gameGuiColor.G, gameGuiColor.B, gameGuiColor.A);
        }
        
        /// <summary>
        /// Create a quick button with Unity-style setup
        /// </summary>
        public static Button CreateButton(string text, UnityEngine.Vector2 position, UnityEngine.Vector2 size, System.Action onClick = null)
        {
            var button = new Button(text)
            {
                Position = ToGameGUIVector2(position),
                Size = ToGameGUIVector2(size)
            };
            
            if (onClick != null)
            {
                button.OnButtonClick += (btn) => onClick();
            }
            
            return button;
        }
        
        /// <summary>
        /// Create a quick label with Unity-style setup
        /// </summary>
        public static Label CreateLabel(string text, UnityEngine.Vector2 position, UnityEngine.Vector2 size)
        {
            return new Label(text)
            {
                Position = ToGameGUIVector2(position),
                Size = ToGameGUIVector2(size)
            };
        }
        
        /// <summary>
        /// Create a quick text box with Unity-style setup
        /// </summary>
        public static TextBox CreateTextBox(string placeholder, UnityEngine.Vector2 position, UnityEngine.Vector2 size)
        {
            return new TextBox(placeholder)
            {
                Position = ToGameGUIVector2(position),
                Size = ToGameGUIVector2(size)
            };
        }
        
        /// <summary>
        /// Create a quick panel with Unity-style setup
        /// </summary>
        public static Panel CreatePanel(UnityEngine.Vector2 position, UnityEngine.Vector2 size, UnityEngine.Color? backgroundColor = null)
        {
            var panel = new Panel
            {
                Position = ToGameGUIVector2(position),
                Size = ToGameGUIVector2(size)
            };
            
            if (backgroundColor.HasValue)
            {
                panel.Style.BackgroundColor = ToGameGUIColor(backgroundColor.Value);
            }
            
            return panel;
        }
        
        /// <summary>
        /// Apply Unity-style theme colors to a theme
        /// </summary>
        public static void ApplyUnityColors(GUITheme theme, UnityEngine.Color primary, UnityEngine.Color secondary, UnityEngine.Color background)
        {
            theme.Colors.Primary = ToGameGUIColor(primary);
            theme.Colors.Secondary = ToGameGUIColor(secondary);
            theme.Colors.Background = ToGameGUIColor(background);
            theme.Colors.Surface = ToGameGUIColor(background * 1.1f);
            theme.Colors.OnPrimary = ToGameGUIColor(UnityEngine.Color.white);
            theme.Colors.OnSecondary = ToGameGUIColor(UnityEngine.Color.white);
            theme.Colors.OnBackground = ToGameGUIColor(UnityEngine.Color.black);
        }
        
        /// <summary>
        /// Get screen-relative position for GUI elements
        /// </summary>
        public static GameGUI.Core.Vector2 GetScreenRelativePosition(float xPercent, float yPercent)
        {
            return new GameGUI.Core.Vector2(
                Screen.width * xPercent,
                Screen.height * yPercent
            );
        }
        
        /// <summary>
        /// Get screen-relative size for GUI elements
        /// </summary>
        public static GameGUI.Core.Vector2 GetScreenRelativeSize(float widthPercent, float heightPercent)
        {
            return new GameGUI.Core.Vector2(
                Screen.width * widthPercent,
                Screen.height * heightPercent
            );
        }
        
        /// <summary>
        /// Create a responsive button that scales with screen size
        /// </summary>
        public static Button CreateResponsiveButton(string text, float xPercent, float yPercent, float widthPercent, float heightPercent, System.Action onClick = null)
        {
            var button = new Button(text)
            {
                Position = GetScreenRelativePosition(xPercent, yPercent),
                Size = GetScreenRelativeSize(widthPercent, heightPercent)
            };
            
            if (onClick != null)
            {
                button.OnButtonClick += (btn) => onClick();
            }
            
            return button;
        }
        
        /// <summary>
        /// Setup a GUI bridge with common Unity settings
        /// </summary>
        public static UnityGUIBridge SetupGUIBridge(GameObject gameObject, UnityEngine.Vector2? referenceResolution = null)
        {
            var bridge = gameObject.GetComponent<UnityGUIBridge>();
            if (bridge == null)
            {
                bridge = gameObject.AddComponent<UnityGUIBridge>();
            }
            
            if (referenceResolution.HasValue)
            {
                bridge.SetReferenceResolution(referenceResolution.Value);
            }
            
            return bridge;
        }
        
        /// <summary>
        /// Create a simple menu layout
        /// </summary>
        public static Panel CreateSimpleMenu(string title, params (string text, System.Action action)[] buttons)
        {
            var menuPanel = CreatePanel(
                GetScreenRelativePosition(0.3f, 0.2f),
                GetScreenRelativeSize(0.4f, 0.6f),
                new UnityEngine.Color(0.2f, 0.2f, 0.2f, 0.9f)
            );
            
            menuPanel.Style.BorderColor = GameGUI.Core.Color.White;
            menuPanel.Style.BorderWidth = 2;
            menuPanel.Style.BorderRadius = 8;
            menuPanel.Style.Padding = new Padding(20);
            
            // Title
            var titleLabel = CreateLabel(title, UnityEngine.Vector2.zero, new UnityEngine.Vector2(menuPanel.Size.X - 40, 40));
            titleLabel.Style.TextColor = GameGUI.Core.Color.White;
            titleLabel.Style.FontSize = 18;
            titleLabel.Style.FontStyle = FontStyle.Bold;
            titleLabel.Style.TextAlignment = TextAlignment.Center;
            menuPanel.AddChild(titleLabel);
            
            // Buttons
            float buttonY = 60;
            foreach (var (text, action) in buttons)
            {
                var button = CreateButton(text, new UnityEngine.Vector2(0, buttonY), new UnityEngine.Vector2(menuPanel.Size.X - 40, 35), action);
                button.Style.BackgroundColor = GameGUI.Core.Color.FromHex("#4A90E2");
                button.Style.HoverStyle = new GUIStyle { BackgroundColor = GameGUI.Core.Color.FromHex("#357ABD") };
                menuPanel.AddChild(button);
                buttonY += 45;
            }
            
            return menuPanel;
        }
    }
}
