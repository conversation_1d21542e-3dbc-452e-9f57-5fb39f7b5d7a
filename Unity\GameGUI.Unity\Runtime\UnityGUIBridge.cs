using GameGUI.Core;
using GameGUI.Events;
using UnityEngine;

namespace GameGUI.Unity
{
    /// <summary>
    /// Unity MonoBehaviour that bridges the pure C# GameGUI system with Unity
    /// </summary>
    public class UnityGUIBridge : MonoBehaviour
    {
        [Header("GUI Settings")]
        [SerializeField] private bool _enableGUI = true;
        [SerializeField] private Camera _targetCamera;
        [SerializeField] private bool _useScreenSpace = true;
        [SerializeField] private Vector2 _referenceResolution = new Vector2(1920, 1080);
        [SerializeField] private bool _scaleWithScreenSize = true;
        
        private GUIManager _guiManager;
        private UnityRenderer _renderer;
        private GUIEventManager _eventManager;
        private bool _isInitialized = false;
        
        public GUIManager GUIManager => _guiManager;
        public UnityRenderer Renderer => _renderer;
        public GUIEventManager EventManager => _eventManager;
        public bool IsInitialized => _isInitialized;
        
        public IGUIElement RootElement
        {
            get => _guiManager?.RootElement;
            set
            {
                if (_guiManager != null)
                {
                    _guiManager.RootElement = value;
                    if (_eventManager != null)
                        _eventManager.RootElement = value;
                }
            }
        }
        
        private void Awake()
        {
            Initialize();
        }
        
        private void Initialize()
        {
            if (_isInitialized)
                return;
                
            // Initialize camera
            if (_targetCamera == null)
                _targetCamera = Camera.main ?? FindObjectOfType<Camera>();
                
            // Initialize renderer
            _renderer = new UnityRenderer(_targetCamera);
            
            // Initialize GUI manager
            _guiManager = new GUIManager(_renderer);
            _guiManager.Initialize();
            
            // Set root element size
            _guiManager.SetRootSize(_referenceResolution);
            
            // Initialize event manager
            _eventManager = new GUIEventManager(_guiManager.RootElement);
            
            _isInitialized = true;
        }
        
        private void Update()
        {
            if (!_enableGUI || !_isInitialized)
                return;
                
            HandleInput();
            _guiManager.Update(Time.deltaTime);
            _eventManager.Update(Time.deltaTime);
        }
        
        private void OnGUI()
        {
            if (!_enableGUI || !_isInitialized)
                return;
                
            // Scale GUI for different screen resolutions
            if (_useScreenSpace && _scaleWithScreenSize)
            {
                var screenSize = new Vector2(Screen.width, Screen.height);
                var scale = Mathf.Min(screenSize.x / _referenceResolution.x, screenSize.y / _referenceResolution.y);
                GUI.matrix = Matrix4x4.Scale(new Vector3(scale, scale, 1));
            }
            
            _guiManager.Render();
        }
        
        private void HandleInput()
        {
            // Handle mouse input
            var mousePosition = GetGUIMousePosition();
            
            // Mouse movement
            _eventManager.HandleMouseMove(mousePosition);
            
            // Mouse buttons
            if (Input.GetMouseButtonDown(0))
                _eventManager.HandleMouseDown(mousePosition, MouseButton.Left);
            if (Input.GetMouseButtonDown(1))
                _eventManager.HandleMouseDown(mousePosition, MouseButton.Right);
            if (Input.GetMouseButtonDown(2))
                _eventManager.HandleMouseDown(mousePosition, MouseButton.Middle);
                
            if (Input.GetMouseButtonUp(0))
                _eventManager.HandleMouseUp(mousePosition, MouseButton.Left);
            if (Input.GetMouseButtonUp(1))
                _eventManager.HandleMouseUp(mousePosition, MouseButton.Right);
            if (Input.GetMouseButtonUp(2))
                _eventManager.HandleMouseUp(mousePosition, MouseButton.Middle);
            
            // Keyboard input
            HandleKeyboardInput();
            
            // Character input
            foreach (char c in Input.inputString)
            {
                if (c == '\b' || c == '\n' || c == '\r') // Handled by keyboard input
                    continue;
                    
                _eventManager.HandleCharacterInput(c);
            }
        }
        
        private void HandleKeyboardInput()
        {
            // Handle special keys
            if (Input.GetKeyDown(KeyCode.Backspace))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.Backspace);
            if (Input.GetKeyDown(KeyCode.Delete))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.Delete);
            if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.Enter);
            if (Input.GetKeyDown(KeyCode.Escape))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.Escape);
            if (Input.GetKeyDown(KeyCode.Tab))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.Tab);
            if (Input.GetKeyDown(KeyCode.LeftArrow))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.LeftArrow);
            if (Input.GetKeyDown(KeyCode.RightArrow))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.RightArrow);
            if (Input.GetKeyDown(KeyCode.UpArrow))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.UpArrow);
            if (Input.GetKeyDown(KeyCode.DownArrow))
                _eventManager.HandleKeyDown(GameGUI.Core.KeyCode.DownArrow);
                
            // Handle key releases
            if (Input.GetKeyUp(KeyCode.Backspace))
                _eventManager.HandleKeyUp(GameGUI.Core.KeyCode.Backspace);
            if (Input.GetKeyUp(KeyCode.Delete))
                _eventManager.HandleKeyUp(GameGUI.Core.KeyCode.Delete);
        }
        
        private GameGUI.Core.Vector2 GetGUIMousePosition()
        {
            var mousePos = Input.mousePosition;
            
            if (_useScreenSpace && _scaleWithScreenSize)
            {
                // Convert screen coordinates to GUI coordinates
                var screenSize = new Vector2(Screen.width, Screen.height);
                var scale = Mathf.Min(screenSize.x / _referenceResolution.x, screenSize.y / _referenceResolution.y);
                
                // Unity's mouse coordinates have origin at bottom-left, GUI has origin at top-left
                mousePos.y = Screen.height - mousePos.y;
                
                return new GameGUI.Core.Vector2(mousePos.x / scale, mousePos.y / scale);
            }
            else
            {
                // Direct screen coordinates
                mousePos.y = Screen.height - mousePos.y;
                return new GameGUI.Core.Vector2(mousePos.x, mousePos.y);
            }
        }
        
        // Public API methods
        public T CreateElement<T>() where T : IGUIElement, new()
        {
            return _guiManager.CreateElement<T>();
        }
        
        public void AddElement(IGUIElement element, IGUIElement parent = null)
        {
            _guiManager.AddElement(element, parent);
        }
        
        public void RemoveElement(IGUIElement element)
        {
            _guiManager.RemoveElement(element);
        }
        
        public IGUIElement FindElement(string name)
        {
            return _guiManager.FindElement(name);
        }
        
        public T FindElement<T>(string name) where T : class, IGUIElement
        {
            return _guiManager.FindElement<T>(name);
        }
        
        public void SetReferenceResolution(Vector2 resolution)
        {
            _referenceResolution = resolution;
            _guiManager?.SetRootSize(resolution);
        }
        
        public void SetGUIEnabled(bool enabled)
        {
            _enableGUI = enabled;
        }
        
        // Unity Editor support
        #if UNITY_EDITOR
        private void OnValidate()
        {
            if (_referenceResolution.x <= 0)
                _referenceResolution.x = 1920;
            if (_referenceResolution.y <= 0)
                _referenceResolution.y = 1080;
        }
        #endif
    }
}
