using GameGUI.Core;
using System;

namespace GameGUI.Elements
{
    /// <summary>
    /// Text input element
    /// </summary>
    public class TextBox : GUIElement
    {
        private string _text = string.Empty;
        private string _placeholder = string.Empty;
        private bool _isFocused = false;
        private int _cursorPosition = 0;
        private float _cursorBlinkTime = 0;
        private bool _showCursor = true;
        private bool _isReadOnly = false;
        private int _maxLength = int.MaxValue;
        
        public string Text
        {
            get => _text;
            set
            {
                var newText = value ?? string.Empty;
                if (newText.Length > MaxLength)
                    newText = newText.Substring(0, MaxLength);
                    
                if (_text != newText)
                {
                    _text = newText;
                    _cursorPosition = Math.Min(_cursorPosition, _text.Length);
                    RaiseTextChanged();
                }
            }
        }
        
        public string Placeholder
        {
            get => _placeholder;
            set
            {
                if (_placeholder != value)
                {
                    _placeholder = value ?? string.Empty;
                    OnPlaceholderChanged();
                }
            }
        }
        
        public bool IsFocused
        {
            get => _isFocused;
            private set
            {
                if (_isFocused != value)
                {
                    _isFocused = value;
                    OnFocusChanged();
                    if (_isFocused)
                        TriggerFocus();
                    else
                        TriggerBlur();
                }
            }
        }
        
        public bool IsReadOnly
        {
            get => _isReadOnly;
            set
            {
                if (_isReadOnly != value)
                {
                    _isReadOnly = value;
                    OnReadOnlyChanged();
                }
            }
        }
        
        public int MaxLength
        {
            get => _maxLength;
            set
            {
                if (_maxLength != value)
                {
                    _maxLength = Math.Max(0, value);
                    if (_text.Length > _maxLength)
                        Text = _text.Substring(0, _maxLength);
                }
            }
        }
        
        public int CursorPosition
        {
            get => _cursorPosition;
            set => _cursorPosition = Math.Max(0, Math.Min(value, Text.Length));
        }
        
        public event Action<TextBox, string>? TextChanged;
        
        public TextBox()
        {
            InitializeDefaultStyle();
            Interactive = true;
        }
        
        public TextBox(string placeholder) : this()
        {
            Placeholder = placeholder;
        }
        
        private void InitializeDefaultStyle()
        {
            Style.BackgroundColor = Color.White;
            Style.BorderColor = Color.FromHex("#CCCCCC");
            Style.BorderWidth = 1;
            Style.BorderRadius = 2;
            Style.TextColor = Color.Black;
            Style.FontSize = 12;
            Style.TextAlignment = TextAlignment.Left;
            Style.TextVerticalAlignment = TextVerticalAlignment.Middle;
            Style.Padding = new Padding(8, 4, 8, 4);
            
            // Focused state
            Style.FocusedStyle = new GUIStyle(Style)
            {
                BorderColor = Color.FromHex("#0078D4"),
                BorderWidth = 2
            };
            
            // Disabled state
            Style.DisabledStyle = new GUIStyle(Style)
            {
                BackgroundColor = Color.FromHex("#F5F5F5"),
                BorderColor = Color.FromHex("#E0E0E0"),
                TextColor = Color.FromHex("#808080")
            };
        }
        
        protected override void OnUpdate(float deltaTime)
        {
            if (IsFocused)
            {
                _cursorBlinkTime += deltaTime;
                if (_cursorBlinkTime >= 0.5f)
                {
                    _showCursor = !_showCursor;
                    _cursorBlinkTime = 0;
                }
            }
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            var currentStyle = GetCurrentStyle();
            
            // Draw background
            if (currentStyle.BackgroundColor.A > 0)
            {
                if (currentStyle.BorderRadius > 0)
                {
                    renderer.DrawRoundedRect(bounds, currentStyle.BorderRadius, currentStyle.BackgroundColor);
                }
                else
                {
                    renderer.DrawRect(bounds, currentStyle.BackgroundColor);
                }
            }
            
            // Draw border
            if (currentStyle.BorderWidth > 0 && currentStyle.BorderColor.A > 0)
            {
                if (currentStyle.BorderRadius > 0)
                {
                    renderer.DrawRoundedRectOutline(bounds, currentStyle.BorderRadius, currentStyle.BorderColor, currentStyle.BorderWidth);
                }
                else
                {
                    renderer.DrawRectOutline(bounds, currentStyle.BorderColor, currentStyle.BorderWidth);
                }
            }
            
            // Draw text or placeholder
            var displayText = !string.IsNullOrEmpty(Text) ? Text : Placeholder;
            var textColor = !string.IsNullOrEmpty(Text) ? currentStyle.TextColor : Color.FromHex("#999999");
            
            if (!string.IsNullOrEmpty(displayText))
            {
                var textPosition = CalculateTextPosition(bounds, currentStyle);
                var textStyle = new GUIStyle(currentStyle) { TextColor = textColor };
                renderer.DrawText(displayText, textPosition, textStyle);
            }
            
            // Draw cursor
            if (IsFocused && _showCursor && !IsReadOnly)
            {
                DrawCursor(renderer, bounds, currentStyle);
            }
        }
        
        private void DrawCursor(IGUIRenderer renderer, Rect bounds, GUIStyle style)
        {
            var textPosition = CalculateTextPosition(bounds, style);
            var cursorText = Text.Substring(0, CursorPosition);
            var cursorOffset = EstimateTextWidth(cursorText, style);
            
            var cursorX = textPosition.X + cursorOffset;
            var cursorY = textPosition.Y;
            var cursorHeight = style.FontSize;
            
            renderer.DrawLine(
                new Vector2(cursorX, cursorY),
                new Vector2(cursorX, cursorY + cursorHeight),
                style.TextColor,
                1
            );
        }
        
        private GUIStyle GetCurrentStyle()
        {
            if (!Enabled)
                return Style.DisabledStyle ?? Style;
            if (IsFocused)
                return Style.FocusedStyle ?? Style;
            return Style;
        }
        
        private Vector2 CalculateTextPosition(Rect bounds, GUIStyle style)
        {
            var contentBounds = new Rect(
                bounds.X + style.Padding.Left,
                bounds.Y + style.Padding.Top,
                bounds.Width - style.Padding.Left - style.Padding.Right,
                bounds.Height - style.Padding.Top - style.Padding.Bottom
            );
            
            float x = contentBounds.X;
            float y = contentBounds.Y + (contentBounds.Height - style.FontSize) * 0.5f;
            
            return new Vector2(x, y);
        }
        
        private float EstimateTextWidth(string text, GUIStyle style)
        {
            if (string.IsNullOrEmpty(text))
                return 0;
            return text.Length * style.FontSize * 0.6f;
        }
        
        public void HandleMouseDown(Vector2 position)
        {
            if (!Enabled || !Interactive)
                return;
                
            if (ContainsPoint(position))
            {
                IsFocused = true;
                // Calculate cursor position from mouse position
                var bounds = GetBounds();
                var textPosition = CalculateTextPosition(bounds, GetCurrentStyle());
                var relativeX = position.X - textPosition.X;
                var charWidth = Style.FontSize * 0.6f;
                CursorPosition = Math.Max(0, Math.Min((int)(relativeX / charWidth), Text.Length));
            }
            else
            {
                IsFocused = false;
            }
        }
        
        public void HandleKeyInput(KeyCode key, char character)
        {
            if (!IsFocused || !Enabled || IsReadOnly)
                return;
                
            switch (key)
            {
                case KeyCode.Backspace:
                    if (CursorPosition > 0)
                    {
                        Text = Text.Remove(CursorPosition - 1, 1);
                        CursorPosition--;
                    }
                    break;
                    
                case KeyCode.Delete:
                    if (CursorPosition < Text.Length)
                    {
                        Text = Text.Remove(CursorPosition, 1);
                    }
                    break;
                    
                case KeyCode.LeftArrow:
                    CursorPosition = Math.Max(0, CursorPosition - 1);
                    break;
                    
                case KeyCode.RightArrow:
                    CursorPosition = Math.Min(Text.Length, CursorPosition + 1);
                    break;
                    
                default:
                    if (char.IsControl(character) || Text.Length >= MaxLength)
                        break;
                        
                    Text = Text.Insert(CursorPosition, character.ToString());
                    CursorPosition++;
                    break;
            }
            
            ResetCursorBlink();
        }
        
        private void ResetCursorBlink()
        {
            _cursorBlinkTime = 0;
            _showCursor = true;
        }
        
        protected virtual void RaiseTextChanged()
        {
            TextChanged?.Invoke(this, Text);
        }
        
        protected virtual void OnPlaceholderChanged() { }
        protected virtual void OnFocusChanged() { }
        protected virtual void OnReadOnlyChanged() { }
    }
}
