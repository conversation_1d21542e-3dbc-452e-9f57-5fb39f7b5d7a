using GameGUI.Core;
using System;
using System.Collections.Generic;

namespace GameGUI.Theming
{
    /// <summary>
    /// Represents a complete GUI theme with styles for different element types
    /// </summary>
    [Serializable]
    public class GUITheme
    {
        public string Name { get; set; } = "Default";
        public string Description { get; set; } = string.Empty;
        
        // Element-specific styles
        public Dictionary<string, GUIStyle> ElementStyles { get; set; } = new Dictionary<string, GUIStyle>();
        
        // Global theme colors
        public ThemeColors Colors { get; set; } = new ThemeColors();
        
        // Global theme settings
        public ThemeSettings Settings { get; set; } = new ThemeSettings();
        
        public GUITheme()
        {
            InitializeDefaultStyles();
        }
        
        public GUITheme(string name) : this()
        {
            Name = name;
        }
        
        private void InitializeDefaultStyles()
        {
            // Button style
            ElementStyles["Button"] = new GUIStyle
            {
                BackgroundColor = Colors.Primary,
                BorderColor = Colors.Border,
                BorderWidth = 1,
                BorderRadius = Settings.DefaultBorderRadius,
                TextColor = Colors.OnPrimary,
                FontSize = Settings.DefaultFontSize,
                FontFamily = Settings.DefaultFontFamily,
                TextAlignment = TextAlignment.Center,
                TextVerticalAlignment = TextVerticalAlignment.Middle,
                Padding = new Padding(12, 8, 12, 8),
                TransitionDuration = 0.2f,
                
                HoverStyle = new GUIStyle
                {
                    BackgroundColor = Colors.PrimaryHover,
                    BorderColor = Colors.BorderHover
                },
                
                PressedStyle = new GUIStyle
                {
                    BackgroundColor = Colors.PrimaryPressed,
                    BorderColor = Colors.BorderPressed
                },
                
                DisabledStyle = new GUIStyle
                {
                    BackgroundColor = Colors.Disabled,
                    BorderColor = Colors.DisabledBorder,
                    TextColor = Colors.OnDisabled
                }
            };
            
            // TextBox style
            ElementStyles["TextBox"] = new GUIStyle
            {
                BackgroundColor = Colors.Surface,
                BorderColor = Colors.Border,
                BorderWidth = 1,
                BorderRadius = Settings.DefaultBorderRadius,
                TextColor = Colors.OnSurface,
                FontSize = Settings.DefaultFontSize,
                FontFamily = Settings.DefaultFontFamily,
                TextAlignment = TextAlignment.Left,
                TextVerticalAlignment = TextVerticalAlignment.Middle,
                Padding = new Padding(8, 6, 8, 6),
                
                FocusedStyle = new GUIStyle
                {
                    BorderColor = Colors.Accent,
                    BorderWidth = 2
                },
                
                DisabledStyle = new GUIStyle
                {
                    BackgroundColor = Colors.Disabled,
                    BorderColor = Colors.DisabledBorder,
                    TextColor = Colors.OnDisabled
                }
            };
            
            // Label style
            ElementStyles["Label"] = new GUIStyle
            {
                BackgroundColor = Color.Transparent,
                TextColor = Colors.OnBackground,
                FontSize = Settings.DefaultFontSize,
                FontFamily = Settings.DefaultFontFamily,
                TextAlignment = TextAlignment.Left,
                TextVerticalAlignment = TextVerticalAlignment.Top,
                Padding = new Padding(0)
            };
            
            // Panel style
            ElementStyles["Panel"] = new GUIStyle
            {
                BackgroundColor = Colors.Surface,
                BorderColor = Colors.Border,
                BorderWidth = 0,
                BorderRadius = Settings.DefaultBorderRadius,
                Padding = new Padding(8)
            };
        }
        
        public GUIStyle GetElementStyle(string elementType)
        {
            return ElementStyles.TryGetValue(elementType, out var style) ? style : new GUIStyle();
        }
        
        public void SetElementStyle(string elementType, GUIStyle style)
        {
            ElementStyles[elementType] = style;
        }
        
        public GUITheme Clone()
        {
            var clone = new GUITheme(Name)
            {
                Description = Description,
                Colors = Colors.Clone(),
                Settings = Settings.Clone()
            };
            
            foreach (var kvp in ElementStyles)
            {
                clone.ElementStyles[kvp.Key] = new GUIStyle(kvp.Value);
            }
            
            return clone;
        }
        
        public void ApplyToElement(IGUIElement element)
        {
            var elementType = element.GetType().Name;
            if (ElementStyles.TryGetValue(elementType, out var style))
            {
                element.Style = new GUIStyle(style);
            }
        }
        
        public static GUITheme CreateDarkTheme()
        {
            var theme = new GUITheme("Dark")
            {
                Description = "Dark theme with blue accents",
                Colors = new ThemeColors
                {
                    Primary = Color.FromHex("#0078D4"),
                    PrimaryHover = Color.FromHex("#106EBE"),
                    PrimaryPressed = Color.FromHex("#005A9E"),
                    Secondary = Color.FromHex("#6B73FF"),
                    Accent = Color.FromHex("#0078D4"),
                    Background = Color.FromHex("#1E1E1E"),
                    Surface = Color.FromHex("#2D2D30"),
                    Border = Color.FromHex("#3F3F46"),
                    BorderHover = Color.FromHex("#0078D4"),
                    BorderPressed = Color.FromHex("#005A9E"),
                    OnPrimary = Color.White,
                    OnSecondary = Color.White,
                    OnBackground = Color.FromHex("#CCCCCC"),
                    OnSurface = Color.FromHex("#CCCCCC"),
                    Disabled = Color.FromHex("#3C3C3C"),
                    DisabledBorder = Color.FromHex("#555555"),
                    OnDisabled = Color.FromHex("#888888")
                }
            };
            
            theme.InitializeDefaultStyles();
            return theme;
        }
        
        public static GUITheme CreateLightTheme()
        {
            var theme = new GUITheme("Light")
            {
                Description = "Light theme with blue accents",
                Colors = new ThemeColors
                {
                    Primary = Color.FromHex("#0078D4"),
                    PrimaryHover = Color.FromHex("#106EBE"),
                    PrimaryPressed = Color.FromHex("#005A9E"),
                    Secondary = Color.FromHex("#6B73FF"),
                    Accent = Color.FromHex("#0078D4"),
                    Background = Color.White,
                    Surface = Color.FromHex("#F8F8F8"),
                    Border = Color.FromHex("#D1D1D1"),
                    BorderHover = Color.FromHex("#0078D4"),
                    BorderPressed = Color.FromHex("#005A9E"),
                    OnPrimary = Color.White,
                    OnSecondary = Color.White,
                    OnBackground = Color.FromHex("#323130"),
                    OnSurface = Color.FromHex("#323130"),
                    Disabled = Color.FromHex("#F3F2F1"),
                    DisabledBorder = Color.FromHex("#EDEBE9"),
                    OnDisabled = Color.FromHex("#A19F9D")
                }
            };
            
            theme.InitializeDefaultStyles();
            return theme;
        }
    }
    
    [Serializable]
    public class ThemeColors
    {
        public Color Primary { get; set; } = Color.FromHex("#0078D4");
        public Color PrimaryHover { get; set; } = Color.FromHex("#106EBE");
        public Color PrimaryPressed { get; set; } = Color.FromHex("#005A9E");
        public Color Secondary { get; set; } = Color.FromHex("#6B73FF");
        public Color Accent { get; set; } = Color.FromHex("#0078D4");
        
        public Color Background { get; set; } = Color.White;
        public Color Surface { get; set; } = Color.FromHex("#F8F8F8");
        
        public Color Border { get; set; } = Color.FromHex("#D1D1D1");
        public Color BorderHover { get; set; } = Color.FromHex("#0078D4");
        public Color BorderPressed { get; set; } = Color.FromHex("#005A9E");
        
        public Color OnPrimary { get; set; } = Color.White;
        public Color OnSecondary { get; set; } = Color.White;
        public Color OnBackground { get; set; } = Color.FromHex("#323130");
        public Color OnSurface { get; set; } = Color.FromHex("#323130");
        
        public Color Disabled { get; set; } = Color.FromHex("#F3F2F1");
        public Color DisabledBorder { get; set; } = Color.FromHex("#EDEBE9");
        public Color OnDisabled { get; set; } = Color.FromHex("#A19F9D");
        
        public ThemeColors Clone()
        {
            return new ThemeColors
            {
                Primary = Primary,
                PrimaryHover = PrimaryHover,
                PrimaryPressed = PrimaryPressed,
                Secondary = Secondary,
                Accent = Accent,
                Background = Background,
                Surface = Surface,
                Border = Border,
                BorderHover = BorderHover,
                BorderPressed = BorderPressed,
                OnPrimary = OnPrimary,
                OnSecondary = OnSecondary,
                OnBackground = OnBackground,
                OnSurface = OnSurface,
                Disabled = Disabled,
                DisabledBorder = DisabledBorder,
                OnDisabled = OnDisabled
            };
        }
    }
    
    [Serializable]
    public class ThemeSettings
    {
        public string DefaultFontFamily { get; set; } = "Arial";
        public float DefaultFontSize { get; set; } = 12;
        public float DefaultBorderRadius { get; set; } = 4;
        public float DefaultSpacing { get; set; } = 8;
        public float DefaultAnimationDuration { get; set; } = 0.2f;
        
        public ThemeSettings Clone()
        {
            return new ThemeSettings
            {
                DefaultFontFamily = DefaultFontFamily,
                DefaultFontSize = DefaultFontSize,
                DefaultBorderRadius = DefaultBorderRadius,
                DefaultSpacing = DefaultSpacing,
                DefaultAnimationDuration = DefaultAnimationDuration
            };
        }
    }
}
