using GameGUI.Core;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace GameGUI.Unity
{
    /// <summary>
    /// Unity implementation of IGUIRenderer using Unity's immediate mode GUI
    /// </summary>
    public class UnityRenderer : IGUIRenderer
    {
        private readonly Stack<Matrix4x4> _transformStack = new Stack<Matrix4x4>();
        private readonly Stack<UnityEngine.Rect> _clipStack = new Stack<UnityEngine.Rect>();
        private readonly Dictionary<string, Texture2D> _textureCache = new Dictionary<string, Texture2D>();
        private readonly Dictionary<string, Font> _fontCache = new Dictionary<string, Font>();
        
        private Camera _camera;
        
        public UnityRenderer(Camera camera = null)
        {
            _camera = camera ?? Camera.main;
        }
        
        public void BeginRender()
        {
            _transformStack.Clear();
            _clipStack.Clear();
            GUI.matrix = Matrix4x4.identity;
        }
        
        public void EndRender()
        {
            GUI.matrix = Matrix4x4.identity;
        }
        
        public void SetClipRect(GameGUI.Core.Rect clipRect)
        {
            var unityRect = ToUnityRect(clipRect);
            _clipStack.Push(unityRect);
            
            if (_clipStack.Count == 1)
            {
                GUI.BeginGroup(unityRect);
            }
            else
            {
                // Intersect with previous clip rect
                var previousClip = _clipStack.Skip(1).First();
                var intersected = IntersectRects(unityRect, previousClip);
                GUI.BeginGroup(intersected);
            }
        }
        
        public void ClearClipRect()
        {
            if (_clipStack.Count > 0)
            {
                _clipStack.Pop();
                GUI.EndGroup();
            }
        }
        
        public void DrawRect(GameGUI.Core.Rect rect, GameGUI.Core.Color color)
        {
            var unityRect = ToUnityRect(rect);
            var unityColor = ToUnityColor(color);
            
            var texture = Texture2D.whiteTexture;
            var oldColor = GUI.color;
            GUI.color = unityColor;
            GUI.DrawTexture(unityRect, texture);
            GUI.color = oldColor;
        }
        
        public void DrawRectOutline(GameGUI.Core.Rect rect, GameGUI.Core.Color color, float lineWidth)
        {
            var unityColor = ToUnityColor(color);
            var oldColor = GUI.color;
            GUI.color = unityColor;
            
            // Draw four lines to form the outline
            var top = new GameGUI.Core.Rect(rect.X, rect.Y, rect.Width, lineWidth);
            var bottom = new GameGUI.Core.Rect(rect.X, rect.Y + rect.Height - lineWidth, rect.Width, lineWidth);
            var left = new GameGUI.Core.Rect(rect.X, rect.Y, lineWidth, rect.Height);
            var right = new GameGUI.Core.Rect(rect.X + rect.Width - lineWidth, rect.Y, lineWidth, rect.Height);
            
            GUI.DrawTexture(ToUnityRect(top), Texture2D.whiteTexture);
            GUI.DrawTexture(ToUnityRect(bottom), Texture2D.whiteTexture);
            GUI.DrawTexture(ToUnityRect(left), Texture2D.whiteTexture);
            GUI.DrawTexture(ToUnityRect(right), Texture2D.whiteTexture);
            
            GUI.color = oldColor;
        }
        
        public void DrawRoundedRect(GameGUI.Core.Rect rect, float radius, GameGUI.Core.Color color)
        {
            // Unity's immediate mode GUI doesn't have built-in rounded rectangles
            // For now, fall back to regular rectangle
            DrawRect(rect, color);
        }
        
        public void DrawRoundedRectOutline(GameGUI.Core.Rect rect, float radius, GameGUI.Core.Color color, float lineWidth)
        {
            // Unity's immediate mode GUI doesn't have built-in rounded rectangles
            // For now, fall back to regular rectangle outline
            DrawRectOutline(rect, color, lineWidth);
        }
        
        public void DrawCircle(GameGUI.Core.Vector2 center, float radius, GameGUI.Core.Color color)
        {
            // Unity's immediate mode GUI doesn't have built-in circle drawing
            // This would require a custom shader or texture
            var rect = new GameGUI.Core.Rect(center.X - radius, center.Y - radius, radius * 2, radius * 2);
            DrawRect(rect, color);
        }
        
        public void DrawCircleOutline(GameGUI.Core.Vector2 center, float radius, GameGUI.Core.Color color, float lineWidth)
        {
            // Unity's immediate mode GUI doesn't have built-in circle drawing
            // This would require a custom shader or texture
            var rect = new GameGUI.Core.Rect(center.X - radius, center.Y - radius, radius * 2, radius * 2);
            DrawRectOutline(rect, color, lineWidth);
        }
        
        public void DrawLine(GameGUI.Core.Vector2 start, GameGUI.Core.Vector2 end, GameGUI.Core.Color color, float lineWidth)
        {
            // Unity's immediate mode GUI doesn't have built-in line drawing
            // This is a simplified implementation
            var direction = end - start;
            var length = direction.Magnitude;
            var angle = Mathf.Atan2(direction.Y, direction.X) * Mathf.Rad2Deg;
            
            var oldMatrix = GUI.matrix;
            var pivot = new UnityEngine.Vector2(start.X, start.Y);
            GUI.matrix = Matrix4x4.TRS(ToUnityVector3(pivot), Quaternion.Euler(0, 0, angle), UnityEngine.Vector3.one);
            
            DrawRect(new GameGUI.Core.Rect(0, -lineWidth * 0.5f, length, lineWidth), color);
            
            GUI.matrix = oldMatrix;
        }
        
        public void DrawText(string text, GameGUI.Core.Vector2 position, GUIStyle style)
        {
            if (string.IsNullOrEmpty(text))
                return;
                
            var unityStyle = CreateUnityGUIStyle(style);
            var unityRect = new UnityEngine.Rect(position.X, position.Y, 1000, 100); // Large enough for text
            
            GUI.Label(unityRect, text, unityStyle);
        }
        
        public GameGUI.Core.Vector2 MeasureText(string text, GUIStyle style)
        {
            if (string.IsNullOrEmpty(text))
                return GameGUI.Core.Vector2.Zero;
                
            var unityStyle = CreateUnityGUIStyle(style);
            var content = new GUIContent(text);
            var size = unityStyle.CalcSize(content);
            
            return new GameGUI.Core.Vector2(size.x, size.y);
        }
        
        public void DrawImage(string imagePath, GameGUI.Core.Rect destRect, GameGUI.Core.Rect? sourceRect = null, GameGUI.Core.Color? tint = null)
        {
            var texture = LoadTexture(imagePath) as Texture2D;
            if (texture != null)
            {
                DrawImage(texture, destRect, sourceRect, tint);
            }
        }
        
        public void DrawImage(object imageResource, GameGUI.Core.Rect destRect, GameGUI.Core.Rect? sourceRect = null, GameGUI.Core.Color? tint = null)
        {
            if (imageResource is Texture2D texture)
            {
                var unityRect = ToUnityRect(destRect);
                var oldColor = GUI.color;
                
                if (tint.HasValue)
                    GUI.color = ToUnityColor(tint.Value);
                
                if (sourceRect.HasValue)
                {
                    var srcRect = ToUnityRect(sourceRect.Value);
                    GUI.DrawTextureWithTexCoords(unityRect, texture, srcRect);
                }
                else
                {
                    GUI.DrawTexture(unityRect, texture);
                }
                
                GUI.color = oldColor;
            }
        }
        
        public void DrawGradient(GameGUI.Core.Rect rect, GameGUI.Core.Color topLeft, GameGUI.Core.Color topRight, GameGUI.Core.Color bottomLeft, GameGUI.Core.Color bottomRight)
        {
            // Unity's immediate mode GUI doesn't have built-in gradient support
            // This would require a custom shader
            // For now, use the average color
            var avgColor = new GameGUI.Core.Color(
                (topLeft.R + topRight.R + bottomLeft.R + bottomRight.R) * 0.25f,
                (topLeft.G + topRight.G + bottomLeft.G + bottomRight.G) * 0.25f,
                (topLeft.B + topRight.B + bottomLeft.B + bottomRight.B) * 0.25f,
                (topLeft.A + topRight.A + bottomLeft.A + bottomRight.A) * 0.25f
            );
            DrawRect(rect, avgColor);
        }
        
        public void DrawShadow(GameGUI.Core.Rect rect, GameGUI.Core.Vector2 offset, float blur, GameGUI.Core.Color color)
        {
            // Simple shadow implementation - just draw a darker rectangle offset
            var shadowRect = new GameGUI.Core.Rect(
                rect.X + offset.X,
                rect.Y + offset.Y,
                rect.Width,
                rect.Height
            );
            var shadowColor = new GameGUI.Core.Color(color.R, color.G, color.B, color.A * 0.5f);
            DrawRect(shadowRect, shadowColor);
        }
        
        public void PushTransform(GameGUI.Core.Vector2 translation, float rotation, GameGUI.Core.Vector2 scale)
        {
            _transformStack.Push(GUI.matrix);
            
            var unityTranslation = ToUnityVector3(translation);
            var unityRotation = Quaternion.Euler(0, 0, rotation);
            var unityScale = new UnityEngine.Vector3(scale.X, scale.Y, 1);
            
            GUI.matrix = GUI.matrix * Matrix4x4.TRS(unityTranslation, unityRotation, unityScale);
        }
        
        public void PopTransform()
        {
            if (_transformStack.Count > 0)
            {
                GUI.matrix = _transformStack.Pop();
            }
        }
        
        public object LoadTexture(string path)
        {
            if (_textureCache.TryGetValue(path, out var cachedTexture))
                return cachedTexture;
                
            var texture = Resources.Load<Texture2D>(path);
            if (texture != null)
            {
                _textureCache[path] = texture;
            }
            
            return texture;
        }
        
        public void UnloadTexture(object texture)
        {
            if (texture is Texture2D tex)
            {
                var pathToRemove = string.Empty;
                foreach (var kvp in _textureCache)
                {
                    if (kvp.Value == tex)
                    {
                        pathToRemove = kvp.Key;
                        break;
                    }
                }
                
                if (!string.IsNullOrEmpty(pathToRemove))
                {
                    _textureCache.Remove(pathToRemove);
                }
            }
        }
        
        public object LoadFont(string fontFamily, float fontSize, FontStyle fontStyle)
        {
            var key = $"{fontFamily}_{fontSize}_{fontStyle}";
            if (_fontCache.TryGetValue(key, out var cachedFont))
                return cachedFont;
                
            var font = Resources.Load<Font>(fontFamily) ?? Resources.GetBuiltinResource<Font>("Arial.ttf");
            if (font != null)
            {
                _fontCache[key] = font;
            }
            
            return font;
        }
        
        public void UnloadFont(object font)
        {
            if (font is Font f)
            {
                var keyToRemove = string.Empty;
                foreach (var kvp in _fontCache)
                {
                    if (kvp.Value == f)
                    {
                        keyToRemove = kvp.Key;
                        break;
                    }
                }
                
                if (!string.IsNullOrEmpty(keyToRemove))
                {
                    _fontCache.Remove(keyToRemove);
                }
            }
        }
        
        public GameGUI.Core.Vector2 GetScreenSize()
        {
            return new GameGUI.Core.Vector2(Screen.width, Screen.height);
        }
        
        public float GetDPIScale()
        {
            return Screen.dpi / 96f; // 96 DPI is the standard
        }
        
        // Helper methods for Unity conversion
        private UnityEngine.Rect ToUnityRect(GameGUI.Core.Rect rect)
        {
            return new UnityEngine.Rect(rect.X, rect.Y, rect.Width, rect.Height);
        }
        
        private UnityEngine.Color ToUnityColor(GameGUI.Core.Color color)
        {
            return new UnityEngine.Color(color.R, color.G, color.B, color.A);
        }
        
        private UnityEngine.Vector3 ToUnityVector3(GameGUI.Core.Vector2 vector)
        {
            return new UnityEngine.Vector3(vector.X, vector.Y, 0);
        }
        
        private UnityEngine.GUIStyle CreateUnityGUIStyle(GUIStyle style)
        {
            var unityStyle = new UnityEngine.GUIStyle();
            
            unityStyle.normal.textColor = ToUnityColor(style.TextColor);
            unityStyle.fontSize = (int)style.FontSize;
            
            // Set alignment
            switch (style.TextAlignment)
            {
                case TextAlignment.Left:
                    unityStyle.alignment = TextAnchor.UpperLeft;
                    break;
                case TextAlignment.Center:
                    unityStyle.alignment = TextAnchor.UpperCenter;
                    break;
                case TextAlignment.Right:
                    unityStyle.alignment = TextAnchor.UpperRight;
                    break;
            }
            
            // Set font style
            switch (style.FontStyle)
            {
                case FontStyle.Bold:
                    unityStyle.fontStyle = UnityEngine.FontStyle.Bold;
                    break;
                case FontStyle.Italic:
                    unityStyle.fontStyle = UnityEngine.FontStyle.Italic;
                    break;
                case FontStyle.BoldItalic:
                    unityStyle.fontStyle = UnityEngine.FontStyle.BoldAndItalic;
                    break;
                default:
                    unityStyle.fontStyle = UnityEngine.FontStyle.Normal;
                    break;
            }
            
            return unityStyle;
        }
        
        private UnityEngine.Rect IntersectRects(UnityEngine.Rect a, UnityEngine.Rect b)
        {
            float left = Mathf.Max(a.x, b.x);
            float top = Mathf.Max(a.y, b.y);
            float right = Mathf.Min(a.x + a.width, b.x + b.width);
            float bottom = Mathf.Min(a.y + a.height, b.y + b.height);
            
            if (right > left && bottom > top)
                return new UnityEngine.Rect(left, top, right - left, bottom - top);
            
            return new UnityEngine.Rect(0, 0, 0, 0);
        }
    }
}
