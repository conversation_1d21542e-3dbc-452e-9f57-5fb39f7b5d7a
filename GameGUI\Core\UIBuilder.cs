using GameGUI.Elements;
using System;
using System.Collections.Generic;

namespace GameGUI.Core
{
    /// <summary>
    /// Fluent API builder for creating UI elements super easily
    /// Makes UI creation as simple as: UI.Button("Click Me").OnClick(() => Debug.Log("Clicked!"))
    /// </summary>
    public static class UI
    {
        // Quick element creation methods
        public static ButtonBuilder Button(string text = "Button") => new ButtonBuilder(text);
        public static LabelBuilder Label(string text = "") => new LabelBuilder(text);
        public static TextBoxBuilder TextBox(string placeholder = "") => new TextBoxBuilder(placeholder);
        public static PanelBuilder Panel() => new PanelBuilder();
        public static ImageBuilder Image(string path = "") => new ImageBuilder(path);
        public static SliderBuilder Slider(float min = 0, float max = 1, float value = 0) => new SliderBuilder(min, max, value);
        public static ProgressBarBuilder ProgressBar(float min = 0, float max = 100, float value = 0) => new ProgressBarBuilder(min, max, value);
        public static ToggleBuilder Toggle(string label = "", bool isOn = false) => new ToggleBuilder(label, isOn);
        public static DropdownBuilder Dropdown(params string[] options) => new DropdownBuilder(options);
        public static ScrollViewBuilder ScrollView() => new ScrollViewBuilder();
        
        // Layout helpers
        public static VerticalLayoutBuilder VerticalLayout() => new VerticalLayoutBuilder();
        public static HorizontalLayoutBuilder HorizontalLayout() => new HorizontalLayoutBuilder();
        public static GridLayoutBuilder GridLayout(int columns) => new GridLayoutBuilder(columns);
    }
    
    // Base builder class
    public abstract class ElementBuilder<T, TBuilder> where T : IGUIElement where TBuilder : ElementBuilder<T, TBuilder>
    {
        protected T element;
        
        public ElementBuilder(T element)
        {
            this.element = element;
        }
        
        public TBuilder At(float x, float y)
        {
            element.Position = new Vector2(x, y);
            return (TBuilder)this;
        }
        
        public TBuilder Size(float width, float height)
        {
            element.Size = new Vector2(width, height);
            return (TBuilder)this;
        }
        
        public TBuilder Width(float width)
        {
            element.Size = new Vector2(width, element.Size.Y);
            return (TBuilder)this;
        }
        
        public TBuilder Height(float height)
        {
            element.Size = new Vector2(element.Size.X, height);
            return (TBuilder)this;
        }
        
        public TBuilder Name(string name)
        {
            element.Name = name;
            return (TBuilder)this;
        }
        
        public TBuilder Visible(bool visible = true)
        {
            element.Visible = visible;
            return (TBuilder)this;
        }
        
        public TBuilder Enabled(bool enabled = true)
        {
            element.Enabled = enabled;
            return (TBuilder)this;
        }
        
        public TBuilder ZOrder(int zOrder)
        {
            element.ZOrder = zOrder;
            return (TBuilder)this;
        }
        
        public TBuilder Style(GUIStyle style)
        {
            element.Style = style;
            return (TBuilder)this;
        }
        
        public TBuilder BackgroundColor(Color color)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.BackgroundColor = color;
            return (TBuilder)this;
        }
        
        public TBuilder TextColor(Color color)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.TextColor = color;
            return (TBuilder)this;
        }
        
        public TBuilder FontSize(float size)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.FontSize = size;
            return (TBuilder)this;
        }
        
        public TBuilder Padding(float all)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.Padding = new Padding(all);
            return (TBuilder)this;
        }
        
        public TBuilder Padding(float horizontal, float vertical)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.Padding = new Padding(horizontal, vertical);
            return (TBuilder)this;
        }
        
        public TBuilder Padding(float left, float top, float right, float bottom)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.Padding = new Padding(left, top, right, bottom);
            return (TBuilder)this;
        }
        
        public TBuilder BorderRadius(float radius)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.BorderRadius = radius;
            return (TBuilder)this;
        }
        
        public TBuilder Border(Color color, float width)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.BorderColor = color;
            element.Style.BorderWidth = width;
            return (TBuilder)this;
        }
        
        public TBuilder OnClick(Action<IGUIElement> callback)
        {
            element.OnClick += callback;
            return (TBuilder)this;
        }
        
        public TBuilder OnHover(Action<IGUIElement> callback)
        {
            element.OnHover += callback;
            return (TBuilder)this;
        }
        
        public T Build() => element;
        
        public static implicit operator T(ElementBuilder<T, TBuilder> builder) => builder.element;
    }
    
    // Specific builders
    public class ButtonBuilder : ElementBuilder<Button, ButtonBuilder>
    {
        public ButtonBuilder(string text) : base(new Button(text)) { }
        
        public ButtonBuilder Text(string text)
        {
            element.Text = text;
            return this;
        }
        
        public ButtonBuilder OnClick(Action<Button> callback)
        {
            element.OnButtonClick += callback;
            return this;
        }
    }
    
    public class LabelBuilder : ElementBuilder<Label, LabelBuilder>
    {
        public LabelBuilder(string text) : base(new Label(text)) { }
        
        public LabelBuilder Text(string text)
        {
            element.Text = text;
            return this;
        }
        
        public LabelBuilder Align(TextAlignment alignment)
        {
            if (element.Style == null)
                element.Style = new GUIStyle();
            element.Style.TextAlignment = alignment;
            return this;
        }
    }
    
    public class TextBoxBuilder : ElementBuilder<TextBox, TextBoxBuilder>
    {
        public TextBoxBuilder(string placeholder) : base(new TextBox(placeholder)) { }

        public TextBoxBuilder Placeholder(string text)
        {
            element.Placeholder = text;
            return this;
        }

        public TextBoxBuilder Text(string text)
        {
            element.Text = text;
            return this;
        }

        public TextBoxBuilder OnTextChanged(Action<TextBox, string> callback)
        {
            element.TextChanged += callback;
            return this;
        }

        public TextBoxBuilder Password(bool isPassword = true)
        {
            // TextBox doesn't have PasswordChar, we'll need to add it or use a different approach
            // For now, just return this
            return this;
        }

        public TextBoxBuilder MaxLength(int length)
        {
            element.MaxLength = length;
            return this;
        }
    }
    
    public class PanelBuilder : ElementBuilder<Panel, PanelBuilder>
    {
        public PanelBuilder() : base(new Panel()) { }
        
        public PanelBuilder Children(params IGUIElement[] children)
        {
            foreach (var child in children)
            {
                element.AddChild(child);
            }
            return this;
        }
    }
    
    public class ImageBuilder : ElementBuilder<Image, ImageBuilder>
    {
        public ImageBuilder(string path) : base(new Image(path)) { }
        
        public ImageBuilder Path(string path)
        {
            element.ImagePath = path;
            return this;
        }
        
        public ImageBuilder Tint(Color color)
        {
            element.TintColor = color;
            return this;
        }
    }
    
    public class SliderBuilder : ElementBuilder<Slider, SliderBuilder>
    {
        public SliderBuilder(float min, float max, float value) : base(new Slider(min, max, value)) { }
        
        public SliderBuilder Value(float value)
        {
            element.Value = value;
            return this;
        }
        
        public SliderBuilder OnValueChanged(Action<Slider, float> callback)
        {
            element.OnValueChanged += callback;
            return this;
        }
        
        public SliderBuilder WholeNumbers(bool whole = true)
        {
            element.WholeNumbers = whole;
            return this;
        }
        
        public SliderBuilder Vertical()
        {
            element.Direction = SliderDirection.Vertical;
            return this;
        }
    }
    
    public class ProgressBarBuilder : ElementBuilder<ProgressBar, ProgressBarBuilder>
    {
        public ProgressBarBuilder(float min, float max, float value) : base(new ProgressBar(min, max, value)) { }
        
        public ProgressBarBuilder Value(float value)
        {
            element.Value = value;
            return this;
        }
        
        public ProgressBarBuilder ShowText(bool show = true)
        {
            element.ShowText = show;
            return this;
        }
        
        public ProgressBarBuilder CustomText(string text)
        {
            element.CustomText = text;
            return this;
        }
    }
    
    public class ToggleBuilder : ElementBuilder<Toggle, ToggleBuilder>
    {
        public ToggleBuilder(string label, bool isOn) : base(new Toggle(label, isOn)) { }
        
        public ToggleBuilder Label(string label)
        {
            element.Label = label;
            return this;
        }
        
        public ToggleBuilder IsOn(bool isOn)
        {
            element.IsOn = isOn;
            return this;
        }
        
        public ToggleBuilder OnValueChanged(Action<Toggle, bool> callback)
        {
            element.OnValueChanged += callback;
            return this;
        }
        
        public ToggleBuilder AsSwitch()
        {
            element.ToggleStyleType = ToggleStyle.Switch;
            return this;
        }
        
        public ToggleBuilder AsRadio()
        {
            element.ToggleStyleType = ToggleStyle.Radio;
            return this;
        }
    }
    
    public class DropdownBuilder : ElementBuilder<Dropdown, DropdownBuilder>
    {
        public DropdownBuilder(string[] options) : base(new Dropdown(new List<string>(options))) { }
        
        public DropdownBuilder Options(params string[] options)
        {
            element.Options = new List<string>(options);
            return this;
        }
        
        public DropdownBuilder SelectedIndex(int index)
        {
            element.SelectedIndex = index;
            return this;
        }
        
        public DropdownBuilder OnSelectionChanged(Action<Dropdown, int> callback)
        {
            element.OnSelectionChanged += callback;
            return this;
        }
    }
    
    public class ScrollViewBuilder : ElementBuilder<ScrollView, ScrollViewBuilder>
    {
        public ScrollViewBuilder() : base(new ScrollView()) { }

        public ScrollViewBuilder ContentSize(float width, float height)
        {
            element.ContentSize = new Vector2(width, height);
            return this;
        }

        public ScrollViewBuilder Children(params IGUIElement[] children)
        {
            foreach (var child in children)
            {
                element.AddContent(child);
            }
            return this;
        }
    }

    // Layout builders
    public class VerticalLayoutBuilder
    {
        private readonly Panel panel;
        private readonly List<IGUIElement> children = new List<IGUIElement>();
        private float spacing = 10f;
        private float padding = 10f;

        public VerticalLayoutBuilder()
        {
            panel = new Panel();
        }

        public VerticalLayoutBuilder At(float x, float y)
        {
            panel.Position = new Vector2(x, y);
            return this;
        }

        public VerticalLayoutBuilder Spacing(float spacing)
        {
            this.spacing = spacing;
            return this;
        }

        public VerticalLayoutBuilder Padding(float padding)
        {
            this.padding = padding;
            return this;
        }

        public VerticalLayoutBuilder Add(IGUIElement element)
        {
            children.Add(element);
            return this;
        }

        public VerticalLayoutBuilder Children(params object[] elements)
        {
            foreach (var elem in elements)
            {
                if (elem is IGUIElement guiElement)
                    children.Add(guiElement);
            }
            return this;
        }

        public Panel Build()
        {
            float currentY = padding;

            foreach (var child in children)
            {
                child.Position = new Vector2(padding, currentY);
                panel.AddChild(child);
                currentY += child.Size.Y + spacing;
            }

            // Auto-size panel
            if (children.Count > 0)
            {
                float maxWidth = 0;
                foreach (var child in children)
                {
                    maxWidth = Math.Max(maxWidth, child.Size.X);
                }
                panel.Size = new Vector2(maxWidth + padding * 2, currentY - spacing + padding);
            }

            return panel;
        }

        public static implicit operator Panel(VerticalLayoutBuilder builder) => builder.Build();
    }

    public class HorizontalLayoutBuilder
    {
        private readonly Panel panel;
        private readonly List<IGUIElement> children = new List<IGUIElement>();
        private float spacing = 10f;
        private float padding = 10f;

        public HorizontalLayoutBuilder()
        {
            panel = new Panel();
        }

        public HorizontalLayoutBuilder At(float x, float y)
        {
            panel.Position = new Vector2(x, y);
            return this;
        }

        public HorizontalLayoutBuilder Spacing(float spacing)
        {
            this.spacing = spacing;
            return this;
        }

        public HorizontalLayoutBuilder Padding(float padding)
        {
            this.padding = padding;
            return this;
        }

        public HorizontalLayoutBuilder Add(IGUIElement element)
        {
            children.Add(element);
            return this;
        }

        public HorizontalLayoutBuilder Children(params object[] elements)
        {
            foreach (var elem in elements)
            {
                if (elem is IGUIElement guiElement)
                    children.Add(guiElement);
            }
            return this;
        }

        public Panel Build()
        {
            float currentX = padding;

            foreach (var child in children)
            {
                child.Position = new Vector2(currentX, padding);
                panel.AddChild(child);
                currentX += child.Size.X + spacing;
            }

            // Auto-size panel
            if (children.Count > 0)
            {
                float maxHeight = 0;
                foreach (var child in children)
                {
                    maxHeight = Math.Max(maxHeight, child.Size.Y);
                }
                panel.Size = new Vector2(currentX - spacing + padding, maxHeight + padding * 2);
            }

            return panel;
        }

        public static implicit operator Panel(HorizontalLayoutBuilder builder) => builder.Build();
    }

    public class GridLayoutBuilder
    {
        private readonly Panel panel;
        private readonly List<IGUIElement> children = new List<IGUIElement>();
        private readonly int columns;
        private float spacing = 10f;
        private float padding = 10f;
        private Vector2 cellSize = new Vector2(100, 100);

        public GridLayoutBuilder(int columns)
        {
            this.columns = columns;
            panel = new Panel();
        }

        public GridLayoutBuilder Spacing(float spacing)
        {
            this.spacing = spacing;
            return this;
        }

        public GridLayoutBuilder Padding(float padding)
        {
            this.padding = padding;
            return this;
        }

        public GridLayoutBuilder CellSize(float width, float height)
        {
            this.cellSize = new Vector2(width, height);
            return this;
        }

        public GridLayoutBuilder Add(IGUIElement element)
        {
            children.Add(element);
            return this;
        }

        public GridLayoutBuilder Children(params object[] elements)
        {
            foreach (var elem in elements)
            {
                if (elem is IGUIElement guiElement)
                    children.Add(guiElement);
            }
            return this;
        }

        public Panel Build()
        {
            for (int i = 0; i < children.Count; i++)
            {
                int row = i / columns;
                int col = i % columns;

                float x = padding + col * (cellSize.X + spacing);
                float y = padding + row * (cellSize.Y + spacing);

                var child = children[i];
                child.Position = new Vector2(x, y);
                child.Size = cellSize;
                panel.AddChild(child);
            }

            // Auto-size panel
            if (children.Count > 0)
            {
                int rows = (children.Count + columns - 1) / columns;
                panel.Size = new Vector2(
                    padding * 2 + columns * cellSize.X + (columns - 1) * spacing,
                    padding * 2 + rows * cellSize.Y + (rows - 1) * spacing
                );
            }

            return panel;
        }

        public static implicit operator Panel(GridLayoutBuilder builder) => builder.Build();
    }
}

