using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using GameGUI.Core;
using GameGUI.Elements;

namespace GameGUI.Unity.Editor
{
    /// <summary>
    /// Visual GUI Designer Editor Window
    /// </summary>
    public class GUIDesignerWindow : EditorWindow
    {
        private GUIDesignAsset currentDesign;
        private GUIElementData selectedElement;
        private Vector2 hierarchyScrollPos;
        private Vector2 propertiesScrollPos;
        private Vector2 canvasScrollPos;
        private Rect canvasRect;
        private float zoom = 1f;
        
        // UI State
        private string[] elementTypes = { "Panel", "Button", "Label", "TextBox", "Image" };
        private int selectedElementTypeIndex = 0;
        
        // Layout
        private float hierarchyWidth = 250f;
        private float propertiesWidth = 300f;
        private bool isDraggingHierarchySplitter = false;
        private bool isDraggingPropertiesSplitter = false;
        
        [MenuItem("Window/GameGUI/GUI Designer")]
        public static void ShowWindow()
        {
            var window = GetWindow<GUIDesignerWindow>("GameGUI Designer");
            window.minSize = new Vector2(1000, 600);
            window.Show();
        }
        
        private void OnEnable()
        {
            canvasRect = new Rect(0, 0, 1920, 1080);
        }
        
        private void OnGUI()
        {
            DrawToolbar();
            
            EditorGUILayout.BeginHorizontal();
            
            // Left Panel - Hierarchy
            DrawHierarchyPanel();
            
            // Splitter
            DrawVerticalSplitter(ref hierarchyWidth, ref isDraggingHierarchySplitter);
            
            // Center - Canvas
            DrawCanvas();
            
            // Splitter
            DrawVerticalSplitter(ref propertiesWidth, ref isDraggingPropertiesSplitter);
            
            // Right Panel - Properties
            DrawPropertiesPanel();
            
            EditorGUILayout.EndHorizontal();
            
            HandleInput();
        }
        
        private void DrawToolbar()
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            
            // New Design
            if (GUILayout.Button("New", EditorStyles.toolbarButton, GUILayout.Width(50)))
            {
                CreateNewDesign();
            }
            
            // Load Design
            if (GUILayout.Button("Load", EditorStyles.toolbarButton, GUILayout.Width(50)))
            {
                LoadDesign();
            }
            
            // Save Design
            if (GUILayout.Button("Save", EditorStyles.toolbarButton, GUILayout.Width(50)))
            {
                SaveDesign();
            }
            
            GUILayout.Space(10);
            
            // Current Design Name
            if (currentDesign != null)
            {
                GUILayout.Label($"Design: {currentDesign.designName}", EditorStyles.toolbarButton);
            }
            else
            {
                GUILayout.Label("No design loaded", EditorStyles.toolbarButton);
            }
            
            GUILayout.FlexibleSpace();
            
            // Generate Script
            if (GUILayout.Button("Generate Script", EditorStyles.toolbarButton, GUILayout.Width(100)))
            {
                GenerateScript();
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void DrawHierarchyPanel()
        {
            EditorGUILayout.BeginVertical(GUILayout.Width(hierarchyWidth));
            
            // Header
            GUILayout.Label("Hierarchy", EditorStyles.boldLabel);
            
            // Add Element Buttons
            EditorGUILayout.BeginHorizontal();
            selectedElementTypeIndex = EditorGUILayout.Popup(selectedElementTypeIndex, elementTypes, GUILayout.Width(150));
            if (GUILayout.Button("Add", GUILayout.Width(80)))
            {
                AddElement(elementTypes[selectedElementTypeIndex]);
            }
            EditorGUILayout.EndHorizontal();
            
            GUILayout.Space(5);
            
            // Element List
            hierarchyScrollPos = EditorGUILayout.BeginScrollView(hierarchyScrollPos);
            
            if (currentDesign != null)
            {
                for (int i = 0; i < currentDesign.rootElements.Count; i++)
                {
                    DrawElementHierarchy(currentDesign.rootElements[i], 0, i);
                }
            }
            else
            {
                GUILayout.Label("No design loaded. Click 'New' to start.", EditorStyles.helpBox);
            }
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawElementHierarchy(GUIElementData element, int depth, int index)
        {
            EditorGUILayout.BeginHorizontal();
            
            GUILayout.Space(depth * 20);
            
            // Selection highlight
            var style = selectedElement == element ? EditorStyles.selectionRect : EditorStyles.label;
            
            if (GUILayout.Button($"{element.elementType}: {element.name}", style, GUILayout.Height(20)))
            {
                selectedElement = element;
                Repaint();
            }
            
            // Delete button
            if (GUILayout.Button("X", GUILayout.Width(20), GUILayout.Height(20)))
            {
                DeleteElement(element);
                return;
            }
            
            EditorGUILayout.EndHorizontal();
            
            // Draw children
            foreach (var child in element.children)
            {
                DrawElementHierarchy(child, depth + 1, 0);
            }
        }
        
        private void DrawCanvas()
        {
            var canvasArea = GUILayoutUtility.GetRect(
                position.width - hierarchyWidth - propertiesWidth - 40,
                position.height - 40,
                GUILayout.ExpandWidth(true),
                GUILayout.ExpandHeight(true)
            );
            
            // Background
            EditorGUI.DrawRect(canvasArea, new UnityEngine.Color(0.2f, 0.2f, 0.2f));
            
            // Canvas info
            GUILayout.BeginArea(canvasArea);
            
            canvasScrollPos = EditorGUILayout.BeginScrollView(canvasScrollPos);
            
            // Draw grid
            DrawGrid(canvasArea);
            
            // Draw elements
            if (currentDesign != null)
            {
                foreach (var element in currentDesign.rootElements)
                {
                    DrawElementOnCanvas(element);
                }
            }
            
            // Canvas controls
            GUILayout.BeginArea(new Rect(10, 10, 200, 60));
            GUILayout.Label($"Canvas: {canvasRect.width} x {canvasRect.height}", EditorStyles.helpBox);
            zoom = EditorGUILayout.Slider("Zoom", zoom, 0.1f, 2f);
            GUILayout.EndArea();
            
            EditorGUILayout.EndScrollView();
            
            GUILayout.EndArea();
        }
        
        private void DrawGrid(Rect area)
        {
            // Simple grid visualization
            Handles.color = new UnityEngine.Color(0.3f, 0.3f, 0.3f);
            
            float gridSize = 50f * zoom;
            
            for (float x = 0; x < area.width; x += gridSize)
            {
                Handles.DrawLine(new Vector3(x, 0), new Vector3(x, area.height));
            }
            
            for (float y = 0; y < area.height; y += gridSize)
            {
                Handles.DrawLine(new Vector3(0, y), new Vector3(area.width, y));
            }
        }
        
        private void DrawElementOnCanvas(GUIElementData element)
        {
            var rect = new Rect(
                element.position.x * zoom,
                element.position.y * zoom,
                element.size.x * zoom,
                element.size.y * zoom
            );
            
            // Draw element background
            UnityEngine.Color bgColor = element.backgroundColor;
            EditorGUI.DrawRect(rect, bgColor);
            
            // Draw border if selected
            if (selectedElement == element)
            {
                Handles.color = UnityEngine.Color.yellow;
                Handles.DrawSolidRectangleWithOutline(rect, UnityEngine.Color.clear, UnityEngine.Color.yellow);
            }
            else if (element.borderWidth > 0)
            {
                Handles.color = element.borderColor;
                Handles.DrawSolidRectangleWithOutline(rect, UnityEngine.Color.clear, element.borderColor);
            }
            
            // Draw text
            if (!string.IsNullOrEmpty(element.text))
            {
                var style = new GUIStyle(GUI.skin.label);
                style.normal.textColor = element.textColor;
                style.fontSize = Mathf.RoundToInt(element.fontSize * zoom);
                style.alignment = TextAnchor.MiddleCenter;
                
                GUI.Label(rect, element.text, style);
            }
            
            // Draw element type label
            var labelRect = new Rect(rect.x, rect.y - 15, rect.width, 15);
            GUI.Label(labelRect, $"{element.elementType}", EditorStyles.miniLabel);
            
            // Draw children
            foreach (var child in element.children)
            {
                DrawElementOnCanvas(child);
            }
        }
        
        private void DrawPropertiesPanel()
        {
            EditorGUILayout.BeginVertical(GUILayout.Width(propertiesWidth));
            
            GUILayout.Label("Properties", EditorStyles.boldLabel);
            
            propertiesScrollPos = EditorGUILayout.BeginScrollView(propertiesScrollPos);
            
            if (selectedElement != null)
            {
                DrawElementProperties(selectedElement);
            }
            else
            {
                GUILayout.Label("Select an element to edit properties", EditorStyles.helpBox);
            }
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawElementProperties(GUIElementData element)
        {
            EditorGUI.BeginChangeCheck();
            
            // Basic Properties
            GUILayout.Label("Basic", EditorStyles.boldLabel);
            element.name = EditorGUILayout.TextField("Name", element.name);
            element.elementType = EditorGUILayout.TextField("Type", element.elementType);
            
            GUILayout.Space(10);
            
            // Transform
            GUILayout.Label("Transform", EditorStyles.boldLabel);
            Vector2 pos = element.position;
            pos = EditorGUILayout.Vector2Field("Position", pos);
            element.position = pos;
            
            Vector2 size = element.size;
            size = EditorGUILayout.Vector2Field("Size", size);
            element.size = size;
            
            GUILayout.Space(10);
            
            // Appearance
            GUILayout.Label("Appearance", EditorStyles.boldLabel);
            UnityEngine.Color bgColor = element.backgroundColor;
            bgColor = EditorGUILayout.ColorField("Background Color", bgColor);
            element.backgroundColor = bgColor;
            
            UnityEngine.Color borderColor = element.borderColor;
            borderColor = EditorGUILayout.ColorField("Border Color", borderColor);
            element.borderColor = borderColor;
            
            element.borderWidth = EditorGUILayout.FloatField("Border Width", element.borderWidth);
            
            GUILayout.Space(10);
            
            // Text Properties
            if (element.elementType == "Button" || element.elementType == "Label" || element.elementType == "TextBox")
            {
                GUILayout.Label("Text", EditorStyles.boldLabel);
                element.text = EditorGUILayout.TextField("Text", element.text);
                
                UnityEngine.Color textColor = element.textColor;
                textColor = EditorGUILayout.ColorField("Text Color", textColor);
                element.textColor = textColor;
                
                element.fontSize = EditorGUILayout.FloatField("Font Size", element.fontSize);
                element.textAlignment = EditorGUILayout.TextField("Alignment", element.textAlignment);
                
                GUILayout.Space(10);
            }
            
            // Element-specific properties
            DrawElementSpecificProperties(element);
            
            // State
            GUILayout.Label("State", EditorStyles.boldLabel);
            element.visible = EditorGUILayout.Toggle("Visible", element.visible);
            element.enabled = EditorGUILayout.Toggle("Enabled", element.enabled);
            element.interactive = EditorGUILayout.Toggle("Interactive", element.interactive);
            
            if (EditorGUI.EndChangeCheck())
            {
                Repaint();
            }
        }
        
        private void DrawElementSpecificProperties(GUIElementData element)
        {
            switch (element.elementType)
            {
                case "TextBox":
                    element.placeholder = EditorGUILayout.TextField("Placeholder", element.placeholder);
                    element.isPassword = EditorGUILayout.Toggle("Is Password", element.isPassword);
                    break;
                    
                case "Image":
                    element.imagePath = EditorGUILayout.TextField("Image Path", element.imagePath);
                    break;
                    
                case "Slider":
                    element.minValue = EditorGUILayout.FloatField("Min Value", element.minValue);
                    element.maxValue = EditorGUILayout.FloatField("Max Value", element.maxValue);
                    element.value = EditorGUILayout.Slider("Value", element.value, element.minValue, element.maxValue);
                    break;
            }
        }
        
        private void DrawVerticalSplitter(ref float width, ref bool isDragging)
        {
            var splitterRect = GUILayoutUtility.GetRect(5, position.height, GUILayout.ExpandHeight(true));
            EditorGUI.DrawRect(splitterRect, new UnityEngine.Color(0.1f, 0.1f, 0.1f));
            EditorGUIUtility.AddCursorRect(splitterRect, MouseCursor.ResizeHorizontal);
            
            if (Event.current.type == EventType.MouseDown && splitterRect.Contains(Event.current.mousePosition))
            {
                isDragging = true;
            }
            
            if (isDragging)
            {
                width = Event.current.mousePosition.x;
                width = Mathf.Clamp(width, 200, position.width - 400);
                Repaint();
            }
            
            if (Event.current.type == EventType.MouseUp)
            {
                isDragging = false;
            }
        }
        
        private void HandleInput()
        {
            // Handle keyboard shortcuts, etc.
        }
        
        // Design Management
        private void CreateNewDesign()
        {
            var path = EditorUtility.SaveFilePanelInProject(
                "Create New GUI Design",
                "NewGUIDesign",
                "asset",
                "Create a new GUI design asset"
            );
            
            if (!string.IsNullOrEmpty(path))
            {
                currentDesign = CreateInstance<GUIDesignAsset>();
                currentDesign.designName = System.IO.Path.GetFileNameWithoutExtension(path);
                AssetDatabase.CreateAsset(currentDesign, path);
                AssetDatabase.SaveAssets();
                EditorUtility.FocusProjectWindow();
                Selection.activeObject = currentDesign;
            }
        }
        
        private void LoadDesign()
        {
            var path = EditorUtility.OpenFilePanel("Load GUI Design", "Assets", "asset");
            if (!string.IsNullOrEmpty(path))
            {
                path = "Assets" + path.Substring(Application.dataPath.Length);
                currentDesign = AssetDatabase.LoadAssetAtPath<GUIDesignAsset>(path);
                selectedElement = null;
                Repaint();
            }
        }
        
        private void SaveDesign()
        {
            if (currentDesign != null)
            {
                EditorUtility.SetDirty(currentDesign);
                AssetDatabase.SaveAssets();
                Debug.Log($"Saved design: {currentDesign.designName}");
            }
        }
        
        private void AddElement(string elementType)
        {
            if (currentDesign == null)
            {
                EditorUtility.DisplayDialog("No Design", "Please create or load a design first.", "OK");
                return;
            }
            
            var newElement = new GUIElementData
            {
                elementType = elementType,
                name = $"{elementType}_{currentDesign.rootElements.Count}",
                position = new SerializableVector2(100, 100),
                size = new SerializableVector2(200, 50),
                backgroundColor = new SerializableColor(0.3f, 0.3f, 0.3f, 1f),
                textColor = new SerializableColor(1f, 1f, 1f, 1f),
                borderColor = new SerializableColor(1f, 1f, 1f, 1f),
                borderWidth = 1f,
                fontSize = 14f,
                text = elementType == "Button" ? "Button" : elementType == "Label" ? "Label" : "",
                visible = true,
                enabled = true,
                interactive = elementType == "Button" || elementType == "TextBox"
            };
            
            if (selectedElement != null)
            {
                selectedElement.children.Add(newElement);
            }
            else
            {
                currentDesign.AddElement(newElement);
            }
            
            selectedElement = newElement;
            EditorUtility.SetDirty(currentDesign);
            Repaint();
        }
        
        private void DeleteElement(GUIElementData element)
        {
            if (currentDesign != null)
            {
                currentDesign.rootElements.Remove(element);
                if (selectedElement == element)
                {
                    selectedElement = null;
                }
                EditorUtility.SetDirty(currentDesign);
                Repaint();
            }
        }
        
        private void GenerateScript()
        {
            if (currentDesign == null)
            {
                EditorUtility.DisplayDialog("No Design", "Please create or load a design first.", "OK");
                return;
            }
            
            var scriptGenerator = new GUIScriptGenerator();
            var script = scriptGenerator.GenerateScript(currentDesign);
            
            var path = EditorUtility.SaveFilePanel(
                "Save Generated Script",
                "Assets",
                $"{currentDesign.designName}.cs",
                "cs"
            );
            
            if (!string.IsNullOrEmpty(path))
            {
                System.IO.File.WriteAllText(path, script);
                AssetDatabase.Refresh();
                Debug.Log($"Generated script: {path}");
            }
        }
    }
}
