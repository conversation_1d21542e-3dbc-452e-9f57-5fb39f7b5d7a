using GameGUI.Core;
using System;

namespace GameGUI.Elements
{
    /// <summary>
    /// Toggle/Checkbox element for boolean input
    /// Super easy to use with automatic styling and animations
    /// </summary>
    public class Toggle : GUIElement
    {
        private bool _isOn;
        private string _label;
        private ToggleStyle _toggleStyle;
        private Label _labelElement;
        
        public bool IsOn
        {
            get => _isOn;
            set
            {
                if (_isOn != value)
                {
                    _isOn = value;
                    OnValueChanged?.Invoke(this, _isOn);
                }
            }
        }
        
        public string Label
        {
            get => _label;
            set
            {
                _label = value;
                if (_labelElement != null)
                    _labelElement.Text = value;
            }
        }
        
        public ToggleStyle ToggleStyleType
        {
            get => _toggleStyle;
            set => _toggleStyle = value;
        }
        
        public float ToggleSize { get; set; } = 20f;
        public float LabelSpacing { get; set; } = 10f;
        
        public GUIStyle CheckboxStyle { get; set; }
        public GUIStyle CheckmarkStyle { get; set; }
        public GUIStyle LabelStyle { get; set; }
        
        public event Action<Toggle, bool> OnValueChanged;
        
        public Toggle(string label = "", bool initialValue = false, ToggleStyle style = ToggleStyle.Checkbox)
        {
            _label = label;
            _isOn = initialValue;
            _toggleStyle = style;
            
            Size = new Vector2(200, 25);
            Interactive = true;
            
            InitializeStyles();
            CreateComponents();
        }
        
        private void InitializeStyles()
        {
            CheckboxStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#FFFFFF"),
                BorderColor = Color.FromHex("#CCCCCC"),
                BorderWidth = 2,
                BorderRadius = 4
            };
            
            CheckmarkStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#2196F3"),
                BorderRadius = 2
            };
            
            LabelStyle = new GUIStyle
            {
                TextColor = Color.FromHex("#333333"),
                FontSize = 14,
                TextAlignment = TextAlignment.Left
            };
        }
        
        private void CreateComponents()
        {
            if (!string.IsNullOrEmpty(_label))
            {
                _labelElement = new Label(_label)
                {
                    Position = new Vector2(ToggleSize + LabelSpacing, 0),
                    Style = LabelStyle
                };
                AddChild(_labelElement);
            }
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            
            switch (_toggleStyle)
            {
                case ToggleStyle.Checkbox:
                    RenderCheckbox(renderer, bounds);
                    break;
                
                case ToggleStyle.Switch:
                    RenderSwitch(renderer, bounds);
                    break;
                
                case ToggleStyle.Radio:
                    RenderRadio(renderer, bounds);
                    break;
            }
        }
        
        private void RenderCheckbox(IGUIRenderer renderer, Rect bounds)
        {
            var checkboxRect = new Rect(bounds.X, bounds.Y, ToggleSize, ToggleSize);
            
            // Draw checkbox background
            var bgColor = _isOn ? CheckmarkStyle.BackgroundColor : CheckboxStyle.BackgroundColor;
            renderer.DrawRoundedRect(checkboxRect, CheckboxStyle.BorderRadius, bgColor);
            
            // Draw border
            var borderColor = _isOn ? CheckmarkStyle.BackgroundColor : CheckboxStyle.BorderColor;
            renderer.DrawRoundedRectOutline(checkboxRect, CheckboxStyle.BorderRadius, 
                borderColor, CheckboxStyle.BorderWidth);
            
            // Draw checkmark if checked
            if (_isOn)
            {
                DrawCheckmark(renderer, checkboxRect);
            }
        }
        
        private void DrawCheckmark(IGUIRenderer renderer, Rect rect)
        {
            var color = Color.FromHex("#FFFFFF");
            float padding = rect.Width * 0.25f;
            
            // Draw checkmark as two lines
            var p1 = new Vector2(rect.X + padding, rect.Y + rect.Height * 0.5f);
            var p2 = new Vector2(rect.X + rect.Width * 0.4f, rect.Y + rect.Height - padding);
            var p3 = new Vector2(rect.X + rect.Width - padding, rect.Y + padding);
            
            renderer.DrawLine(p1, p2, color, 2f);
            renderer.DrawLine(p2, p3, color, 2f);
        }
        
        private void RenderSwitch(IGUIRenderer renderer, Rect bounds)
        {
            float switchWidth = ToggleSize * 2f;
            float switchHeight = ToggleSize;
            var switchRect = new Rect(bounds.X, bounds.Y, switchWidth, switchHeight);
            
            // Draw switch background
            var bgColor = _isOn ? CheckmarkStyle.BackgroundColor : CheckboxStyle.BackgroundColor;
            renderer.DrawRoundedRect(switchRect, switchHeight * 0.5f, bgColor);
            
            // Draw switch handle
            float handleSize = switchHeight * 0.8f;
            float handleX = _isOn 
                ? switchRect.X + switchWidth - handleSize - (switchHeight - handleSize) * 0.5f
                : switchRect.X + (switchHeight - handleSize) * 0.5f;
            float handleY = switchRect.Y + (switchHeight - handleSize) * 0.5f;
            
            var handleRect = new Rect(handleX, handleY, handleSize, handleSize);
            renderer.DrawCircle(
                new Vector2(handleX + handleSize * 0.5f, handleY + handleSize * 0.5f),
                handleSize * 0.5f,
                Color.FromHex("#FFFFFF")
            );
        }
        
        private void RenderRadio(IGUIRenderer renderer, Rect bounds)
        {
            var radioRect = new Rect(bounds.X, bounds.Y, ToggleSize, ToggleSize);
            var center = new Vector2(
                radioRect.X + radioRect.Width * 0.5f,
                radioRect.Y + radioRect.Height * 0.5f
            );
            float radius = ToggleSize * 0.5f;
            
            // Draw radio background
            renderer.DrawCircle(center, radius, CheckboxStyle.BackgroundColor);
            renderer.DrawCircleOutline(center, radius, CheckboxStyle.BorderColor, CheckboxStyle.BorderWidth);
            
            // Draw inner circle if checked
            if (_isOn)
            {
                float innerRadius = radius * 0.5f;
                renderer.DrawCircle(center, innerRadius, CheckmarkStyle.BackgroundColor);
            }
        }
        
        public void ToggleValue()
        {
            IsOn = !IsOn;
        }

        public void SetValue(bool value, bool silent = false)
        {
            if (silent)
            {
                _isOn = value;
            }
            else
            {
                IsOn = value;
            }
        }
    }
    
    public enum ToggleStyle
    {
        Checkbox,
        Switch,
        Radio
    }
    
    /// <summary>
    /// Toggle group for managing mutually exclusive toggles (radio buttons)
    /// </summary>
    public class ToggleGroup
    {
        private System.Collections.Generic.List<Toggle> _toggles;
        private Toggle _selectedToggle;
        
        public Toggle SelectedToggle => _selectedToggle;
        public int SelectedIndex => _toggles.IndexOf(_selectedToggle);
        
        public event Action<ToggleGroup, Toggle> OnSelectionChanged;
        
        public ToggleGroup()
        {
            _toggles = new System.Collections.Generic.List<Toggle>();
        }
        
        public void AddToggle(Toggle toggle)
        {
            if (!_toggles.Contains(toggle))
            {
                _toggles.Add(toggle);
                toggle.OnValueChanged += HandleToggleChanged;
                
                if (_selectedToggle == null && toggle.IsOn)
                {
                    _selectedToggle = toggle;
                }
            }
        }
        
        public void RemoveToggle(Toggle toggle)
        {
            if (_toggles.Contains(toggle))
            {
                toggle.OnValueChanged -= HandleToggleChanged;
                _toggles.Remove(toggle);
                
                if (_selectedToggle == toggle)
                {
                    _selectedToggle = null;
                }
            }
        }
        
        private void HandleToggleChanged(Toggle toggle, bool isOn)
        {
            if (isOn)
            {
                // Turn off all other toggles
                foreach (var t in _toggles)
                {
                    if (t != toggle && t.IsOn)
                    {
                        t.SetValue(false, silent: true);
                    }
                }
                
                _selectedToggle = toggle;
                OnSelectionChanged?.Invoke(this, toggle);
            }
            else if (_selectedToggle == toggle)
            {
                // Don't allow deselecting the current toggle in a group
                toggle.SetValue(true, silent: true);
            }
        }
        
        public void SelectToggle(int index)
        {
            if (index >= 0 && index < _toggles.Count)
            {
                _toggles[index].IsOn = true;
            }
        }
        
        public void ClearSelection()
        {
            foreach (var toggle in _toggles)
            {
                toggle.SetValue(false, silent: true);
            }
            _selectedToggle = null;
        }
    }
}

