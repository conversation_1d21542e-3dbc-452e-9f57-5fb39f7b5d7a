using GameGUI.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GameGUI.Elements
{
    /// <summary>
    /// Dropdown/ComboBox element for selecting from a list of options
    /// Super easy to use with automatic popup management
    /// </summary>
    public class Dropdown : GUIElement
    {
        private List<string> _options;
        private int _selectedIndex;
        private bool _isOpen;
        private Panel _dropdownPanel;
        private Label _selectedLabel;
        private List<Button> _optionButtons;
        
        public List<string> Options
        {
            get => _options;
            set
            {
                _options = value ?? new List<string>();
                if (_selectedIndex >= _options.Count)
                    _selectedIndex = _options.Count > 0 ? 0 : -1;
                UpdateSelectedLabel();
                RebuildOptions();
            }
        }
        
        public int SelectedIndex
        {
            get => _selectedIndex;
            set
            {
                if (value >= -1 && value < _options.Count && _selectedIndex != value)
                {
                    _selectedIndex = value;
                    UpdateSelectedLabel();
                    OnSelectionChanged?.Invoke(this, _selectedIndex);
                }
            }
        }
        
        public string? SelectedOption
        {
            get => _selectedIndex >= 0 && _selectedIndex < _options.Count ? _options[_selectedIndex] : null;
            set
            {
                if (value != null)
                {
                    var index = _options.IndexOf(value);
                    if (index >= 0)
                        SelectedIndex = index;
                }
            }
        }
        
        public bool IsOpen
        {
            get => _isOpen;
            set
            {
                if (_isOpen != value)
                {
                    _isOpen = value;
                    if (_dropdownPanel != null)
                        _dropdownPanel.Visible = _isOpen;
                    
                    if (_isOpen)
                        OnDropdownOpened?.Invoke(this);
                    else
                        OnDropdownClosed?.Invoke(this);
                }
            }
        }
        
        public float MaxDropdownHeight { get; set; } = 200f;
        public float OptionHeight { get; set; } = 30f;
        
        public GUIStyle DropdownStyle { get; set; }
        public GUIStyle OptionStyle { get; set; }
        public GUIStyle SelectedOptionStyle { get; set; }
        
        public event Action<Dropdown, int> OnSelectionChanged;
        public event Action<Dropdown> OnDropdownOpened;
        public event Action<Dropdown> OnDropdownClosed;
        
        public Dropdown(List<string> options = null, int selectedIndex = 0)
        {
            _options = options ?? new List<string>();
            _selectedIndex = selectedIndex >= 0 && selectedIndex < _options.Count ? selectedIndex : -1;
            _optionButtons = new List<Button>();
            
            Size = new Vector2(200, 35);
            
            InitializeStyles();
            CreateComponents();
        }
        
        private void InitializeStyles()
        {
            DropdownStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#FFFFFF"),
                BorderColor = Color.FromHex("#CCCCCC"),
                BorderWidth = 1,
                BorderRadius = 4,
                Padding = new Padding(10, 8, 30, 8),
                TextColor = Color.FromHex("#333333"),
                FontSize = 14
            };
            
            OptionStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#FFFFFF"),
                TextColor = Color.FromHex("#333333"),
                FontSize = 14,
                Padding = new Padding(10, 8, 10, 8)
            };
            
            SelectedOptionStyle = new GUIStyle
            {
                BackgroundColor = Color.FromHex("#E3F2FD"),
                TextColor = Color.FromHex("#1976D2"),
                FontSize = 14,
                Padding = new Padding(10, 8, 10, 8)
            };
        }
        
        private void CreateComponents()
        {
            // Create selected label
            _selectedLabel = new Label(SelectedOption ?? "Select...")
            {
                Position = new Vector2(DropdownStyle.Padding.Left, DropdownStyle.Padding.Top),
                Style = new GUIStyle
                {
                    TextColor = DropdownStyle.TextColor,
                    FontSize = DropdownStyle.FontSize,
                    TextAlignment = TextAlignment.Left
                }
            };
            AddChild(_selectedLabel);
            
            // Create dropdown panel (initially hidden)
            _dropdownPanel = new Panel
            {
                Position = new Vector2(0, Size.Y),
                Visible = false,
                ZOrder = 1000, // Render on top
                Style = new GUIStyle
                {
                    BackgroundColor = Color.FromHex("#FFFFFF"),
                    BorderColor = Color.FromHex("#CCCCCC"),
                    BorderWidth = 1,
                    BorderRadius = 4
                }
            };
            AddChild(_dropdownPanel);
            
            RebuildOptions();
        }
        
        private void RebuildOptions()
        {
            if (_dropdownPanel == null)
                return;
            
            // Clear existing options
            _dropdownPanel.Children.Clear();
            _optionButtons.Clear();
            
            // Calculate dropdown size
            float dropdownHeight = Math.Min(_options.Count * OptionHeight, MaxDropdownHeight);
            _dropdownPanel.Size = new Vector2(Size.X, dropdownHeight);
            
            // Create option buttons
            for (int i = 0; i < _options.Count; i++)
            {
                int index = i; // Capture for closure
                var option = _options[i];
                
                var button = new Button(option)
                {
                    Position = new Vector2(0, i * OptionHeight),
                    Size = new Vector2(Size.X, OptionHeight),
                    Style = i == _selectedIndex ? SelectedOptionStyle : OptionStyle
                };
                
                button.OnButtonClick += (btn) =>
                {
                    SelectedIndex = index;
                    IsOpen = false;
                };
                
                _dropdownPanel.AddChild(button);
                _optionButtons.Add(button);
            }
        }
        
        private void UpdateSelectedLabel()
        {
            if (_selectedLabel != null)
            {
                _selectedLabel.Text = SelectedOption ?? "Select...";
            }
            
            // Update option button styles
            for (int i = 0; i < _optionButtons.Count; i++)
            {
                _optionButtons[i].Style = i == _selectedIndex ? SelectedOptionStyle : OptionStyle;
            }
        }
        
        protected override void OnRender(IGUIRenderer renderer)
        {
            var bounds = GetBounds();
            
            // Draw background
            renderer.DrawRoundedRect(bounds, DropdownStyle.BorderRadius, DropdownStyle.BackgroundColor);
            if (DropdownStyle.BorderWidth > 0)
            {
                renderer.DrawRoundedRectOutline(bounds, DropdownStyle.BorderRadius,
                    DropdownStyle.BorderColor, DropdownStyle.BorderWidth);
            }
            
            // Draw arrow indicator
            DrawArrow(renderer, bounds);
        }
        
        private void DrawArrow(IGUIRenderer renderer, Rect bounds)
        {
            float arrowSize = 8f;
            float arrowX = bounds.X + bounds.Width - 20f;
            float arrowY = bounds.Y + bounds.Height * 0.5f;
            
            if (_isOpen)
            {
                // Up arrow
                var p1 = new Vector2(arrowX - arrowSize * 0.5f, arrowY + arrowSize * 0.25f);
                var p2 = new Vector2(arrowX + arrowSize * 0.5f, arrowY + arrowSize * 0.25f);
                var p3 = new Vector2(arrowX, arrowY - arrowSize * 0.25f);
                
                renderer.DrawLine(p1, p3, DropdownStyle.TextColor, 2f);
                renderer.DrawLine(p2, p3, DropdownStyle.TextColor, 2f);
            }
            else
            {
                // Down arrow
                var p1 = new Vector2(arrowX - arrowSize * 0.5f, arrowY - arrowSize * 0.25f);
                var p2 = new Vector2(arrowX + arrowSize * 0.5f, arrowY - arrowSize * 0.25f);
                var p3 = new Vector2(arrowX, arrowY + arrowSize * 0.25f);
                
                renderer.DrawLine(p1, p3, DropdownStyle.TextColor, 2f);
                renderer.DrawLine(p2, p3, DropdownStyle.TextColor, 2f);
            }
        }
        
        public void Toggle()
        {
            IsOpen = !IsOpen;
        }
        
        public void AddOption(string option)
        {
            _options.Add(option);
            RebuildOptions();
        }
        
        public void RemoveOption(string option)
        {
            var index = _options.IndexOf(option);
            if (index >= 0)
            {
                RemoveOptionAt(index);
            }
        }
        
        public void RemoveOptionAt(int index)
        {
            if (index >= 0 && index < _options.Count)
            {
                _options.RemoveAt(index);
                if (_selectedIndex == index)
                    _selectedIndex = -1;
                else if (_selectedIndex > index)
                    _selectedIndex--;
                
                RebuildOptions();
                UpdateSelectedLabel();
            }
        }
        
        public void ClearOptions()
        {
            _options.Clear();
            _selectedIndex = -1;
            RebuildOptions();
            UpdateSelectedLabel();
        }
    }
}

