using GameGUI.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GameGUI.Layout
{
    /// <summary>
    /// Layout manager that arranges elements in a vertical or horizontal stack
    /// </summary>
    public class StackLayout : LayoutManager
    {
        public StackDirection Direction { get; set; } = StackDirection.Vertical;
        public float Spacing { get; set; } = 0;
        public StackAlignment Alignment { get; set; } = StackAlignment.Start;
        public bool FillContainer { get; set; } = false;
        
        public StackLayout()
        {
        }
        
        public StackLayout(StackDirection direction, float spacing = 0)
        {
            Direction = direction;
            Spacing = spacing;
        }
        
        public override void CalculateLayout(IGUIElement container, IList<IGUIElement> children)
        {
            if (children == null || children.Count == 0)
                return;
                
            var contentBounds = GetContentBounds(container);
            var visibleChildren = children.Where(c => c.Visible).ToList();
            
            if (visibleChildren.Count == 0)
                return;
                
            if (Direction == StackDirection.Vertical)
            {
                LayoutVertical(contentBounds, visibleChildren);
            }
            else
            {
                LayoutHorizontal(contentBounds, visibleChildren);
            }
        }
        
        private void LayoutVertical(Rect contentBounds, List<IGUIElement> children)
        {
            float totalSpacing = (children.Count - 1) * Spacing;
            float availableHeight = contentBounds.Height - totalSpacing;
            
            if (FillContainer)
            {
                // Distribute available height equally among children
                float childHeight = availableHeight / children.Count;
                float currentY = contentBounds.Y;
                
                foreach (var child in children)
                {
                    var childX = CalculateChildX(contentBounds, child);
                    child.Position = new Vector2(childX, currentY);
                    child.Size = new Vector2(child.Size.X, childHeight);
                    currentY += childHeight + Spacing;
                }
            }
            else
            {
                // Use preferred sizes
                float currentY = contentBounds.Y;
                
                // Apply alignment
                if (Alignment == StackAlignment.Center || Alignment == StackAlignment.End)
                {
                    float totalHeight = children.Sum(c => c.Size.Y) + totalSpacing;
                    if (Alignment == StackAlignment.Center)
                        currentY += (availableHeight - totalHeight) * 0.5f;
                    else if (Alignment == StackAlignment.End)
                        currentY += availableHeight - totalHeight;
                }
                
                foreach (var child in children)
                {
                    var childX = CalculateChildX(contentBounds, child);
                    child.Position = new Vector2(childX, currentY);
                    currentY += child.Size.Y + Spacing;
                }
            }
        }
        
        private void LayoutHorizontal(Rect contentBounds, List<IGUIElement> children)
        {
            float totalSpacing = (children.Count - 1) * Spacing;
            float availableWidth = contentBounds.Width - totalSpacing;
            
            if (FillContainer)
            {
                // Distribute available width equally among children
                float childWidth = availableWidth / children.Count;
                float currentX = contentBounds.X;
                
                foreach (var child in children)
                {
                    var childY = CalculateChildY(contentBounds, child);
                    child.Position = new Vector2(currentX, childY);
                    child.Size = new Vector2(childWidth, child.Size.Y);
                    currentX += childWidth + Spacing;
                }
            }
            else
            {
                // Use preferred sizes
                float currentX = contentBounds.X;
                
                // Apply alignment
                if (Alignment == StackAlignment.Center || Alignment == StackAlignment.End)
                {
                    float totalWidth = children.Sum(c => c.Size.X) + totalSpacing;
                    if (Alignment == StackAlignment.Center)
                        currentX += (availableWidth - totalWidth) * 0.5f;
                    else if (Alignment == StackAlignment.End)
                        currentX += availableWidth - totalWidth;
                }
                
                foreach (var child in children)
                {
                    var childY = CalculateChildY(contentBounds, child);
                    child.Position = new Vector2(currentX, childY);
                    currentX += child.Size.X + Spacing;
                }
            }
        }
        
        private float CalculateChildX(Rect contentBounds, IGUIElement child)
        {
            // For vertical layout, position horizontally based on alignment
            switch (Alignment)
            {
                case StackAlignment.Center:
                    return contentBounds.X + (contentBounds.Width - child.Size.X) * 0.5f;
                case StackAlignment.End:
                    return contentBounds.X + contentBounds.Width - child.Size.X;
                default:
                    return contentBounds.X;
            }
        }
        
        private float CalculateChildY(Rect contentBounds, IGUIElement child)
        {
            // For horizontal layout, position vertically based on alignment
            switch (Alignment)
            {
                case StackAlignment.Center:
                    return contentBounds.Y + (contentBounds.Height - child.Size.Y) * 0.5f;
                case StackAlignment.End:
                    return contentBounds.Y + contentBounds.Height - child.Size.Y;
                default:
                    return contentBounds.Y;
            }
        }
        
        public override Vector2 GetPreferredSize(IGUIElement container, IList<IGUIElement> children)
        {
            if (children == null || children.Count == 0)
                return container.Size;
                
            var visibleChildren = children.Where(c => c.Visible).ToList();
            if (visibleChildren.Count == 0)
                return container.Size;
                
            float totalSpacing = (visibleChildren.Count - 1) * Spacing;
            var style = container.Style;
            
            if (Direction == StackDirection.Vertical)
            {
                float totalHeight = visibleChildren.Sum(c => c.Size.Y) + totalSpacing;
                float maxWidth = visibleChildren.Max(c => c.Size.X);
                
                return new Vector2(
                    maxWidth + style.Padding.Left + style.Padding.Right,
                    totalHeight + style.Padding.Top + style.Padding.Bottom
                );
            }
            else
            {
                float totalWidth = visibleChildren.Sum(c => c.Size.X) + totalSpacing;
                float maxHeight = visibleChildren.Max(c => c.Size.Y);
                
                return new Vector2(
                    totalWidth + style.Padding.Left + style.Padding.Right,
                    maxHeight + style.Padding.Top + style.Padding.Bottom
                );
            }
        }
    }
    
    public enum StackDirection
    {
        Vertical,
        Horizontal
    }
    
    public enum StackAlignment
    {
        Start,
        Center,
        End
    }
}
