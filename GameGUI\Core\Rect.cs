using System;

namespace GameGUI.Core
{
    /// <summary>
    /// Rectangle structure for defining bounds and areas
    /// </summary>
    [Serializable]
    public struct Rect : IEquatable<Rect>
    {
        public float X;
        public float Y;
        public float Width;
        public float Height;
        
        public static readonly Rect Zero = new Rect(0, 0, 0, 0);
        
        public Rect(float x, float y, float width, float height)
        {
            X = x;
            Y = y;
            Width = width;
            Height = height;
        }
        
        public Rect(Vector2 position, Vector2 size)
        {
            X = position.X;
            Y = position.Y;
            Width = size.X;
            Height = size.Y;
        }
        
        public Vector2 Position
        {
            get => new Vector2(X, Y);
            set { X = value.X; Y = value.Y; }
        }
        
        public Vector2 Size
        {
            get => new Vector2(Width, Height);
            set { Width = value.X; Height = value.Y; }
        }
        
        public Vector2 Center => new Vector2(X + Width * 0.5f, Y + Height * 0.5f);
        public Vector2 Min => new Vector2(X, Y);
        public Vector2 Max => new Vector2(X + Width, Y + Height);
        
        public float Left => X;
        public float Right => X + Width;
        public float Top => Y;
        public float Bottom => Y + Height;
        
        public bool Contains(Vector2 point)
        {
            return point.X >= X && point.X <= X + Width &&
                   point.Y >= Y && point.Y <= Y + Height;
        }
        
        public bool Contains(Rect other)
        {
            return other.X >= X && other.Y >= Y &&
                   other.X + other.Width <= X + Width &&
                   other.Y + other.Height <= Y + Height;
        }
        
        public bool Intersects(Rect other)
        {
            return !(other.X > X + Width || other.X + other.Width < X ||
                     other.Y > Y + Height || other.Y + other.Height < Y);
        }
        
        public Rect Intersection(Rect other)
        {
            float left = Math.Max(X, other.X);
            float top = Math.Max(Y, other.Y);
            float right = Math.Min(X + Width, other.X + other.Width);
            float bottom = Math.Min(Y + Height, other.Y + other.Height);
            
            if (right > left && bottom > top)
                return new Rect(left, top, right - left, bottom - top);
            
            return Zero;
        }
        
        public static bool operator ==(Rect a, Rect b) => a.Equals(b);
        public static bool operator !=(Rect a, Rect b) => !a.Equals(b);
        
        public bool Equals(Rect other)
        {
            return Math.Abs(X - other.X) < float.Epsilon &&
                   Math.Abs(Y - other.Y) < float.Epsilon &&
                   Math.Abs(Width - other.Width) < float.Epsilon &&
                   Math.Abs(Height - other.Height) < float.Epsilon;
        }
        
        public override bool Equals(object obj) => obj is Rect other && Equals(other);
        public override int GetHashCode() => HashCode.Combine(X, Y, Width, Height);
        public override string ToString() => $"Rect({X:F2}, {Y:F2}, {Width:F2}, {Height:F2})";
    }
}
